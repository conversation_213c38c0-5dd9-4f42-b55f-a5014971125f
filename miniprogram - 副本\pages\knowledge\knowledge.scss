/**knowledge.scss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin: 30rpx 0;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  background: #4A90E2;
  color: white;
  padding: 0 32rpx;
  height: 80rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

/* 分类标签 */
.category-tabs {
  margin-bottom: 30rpx;
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background: white;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

/* 知识列表 */
.knowledge-list {
  margin-bottom: 40rpx;
}

.knowledge-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.knowledge-thumb {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.knowledge-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.knowledge-header {
  margin-bottom: 12rpx;
}

.knowledge-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
}

.knowledge-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.tag {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.category-tag {
  background: #E3F2FD;
  color: #1976D2;
}

.age-tag {
  background: #F3E5F5;
  color: #7B1FA2;
}

.knowledge-summary {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -webkit-box;
}

.knowledge-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.author {
  font-size: 22rpx;
  color: #999;
}

.publish-time {
  font-size: 22rpx;
  color: #999;
}

.meta-right {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.loading {
  color: #999;
  font-size: 26rpx;
}

.load-more-btn {
  background: white;
  color: #4A90E2;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  display: inline-block;
  border: 2rpx solid #4A90E2;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  display: block;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  z-index: 100;
}

.fab-btn {
  width: 100rpx;
  height: 100rpx;
  background: #4A90E2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.4);
}

.fab-icon {
  width: 48rpx;
  height: 48rpx;
}
