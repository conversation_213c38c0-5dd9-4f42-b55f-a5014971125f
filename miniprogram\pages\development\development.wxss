/* development.wxss */

.container {
  padding: 20rpx;
  background-color: var(--bg-color);
  min-height: 100vh;
}

/* 头部样式 */
.header {
  margin-bottom: 30rpx;
}

.header .card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.header .icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}

.subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 年龄选择器 */
.age-selector {
  margin-bottom: 30rpx;
}

.age-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}

.age-tab {
  flex: 1;
  min-width: 160rpx;
  padding: 20rpx 15rpx;
  background: white;
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.age-tab.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.age-tab:hover {
  border-color: var(--primary-color);
  transform: translateY(-2rpx);
}

.age-icon {
  display: block;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.age-text {
  font-size: 24rpx;
  font-weight: 500;
}

.age-tab.active .age-text {
  color: white;
}
/* 发育里程碑 */
.milestones-section {
  margin-bottom: 30rpx;
}

.milestones-list {
  padding: 20rpx 0;
}

.milestone-category {
  margin-bottom: 40rpx;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.category-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.category-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.milestone-items {
  padding-left: 20rpx;
}

.milestone-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
  padding: 10rpx 0;
}

.milestone-bullet {
  color: var(--primary-color);
  font-weight: bold;
  margin-right: 15rpx;
  margin-top: 5rpx;
  font-size: 24rpx;
}

.milestone-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-primary);
}

/* 注意事项 */
.notice-section {
  margin-bottom: 30rpx;
}

.notice-list {
  padding: 20rpx 0;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background: #fff3cd;
  border-radius: 12rpx;
  border-left: 6rpx solid #ffc107;
}

.notice-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  margin-top: 2rpx;
}

.notice-text {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.5;
  color: #856404;
}

/* 发育促进建议 */
.suggestions-section {
  margin-bottom: 30rpx;
}

.suggestions-list {
  padding: 20rpx 0;
}

.suggestion-category {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
}

.suggestion-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.suggestion-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.suggestion-title {
  font-size: 28rpx;
  font-weight: bold;
}

.suggestion-content {
  padding-left: 40rpx;
}

.suggestion-text {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.95;
}

/* 相关资源 */
.resources-section {
  margin-bottom: 30rpx;
}

.resources-list {
  padding: 20rpx 0;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid var(--border-color);
  transition: all 0.3s ease;
}

.resource-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(79, 70, 229, 0.15);
}

.resource-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.resource-content {
  flex: 1;
}

.resource-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 5rpx;
}

.resource-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .age-tabs {
    flex-direction: column;
  }

  .age-tab {
    min-width: auto;
  }

  .suggestion-category {
    margin: 0 -10rpx 20rpx;
  }
}

/* 动画效果 */
.milestone-item,
.notice-item,
.suggestion-category,
.resource-item {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}