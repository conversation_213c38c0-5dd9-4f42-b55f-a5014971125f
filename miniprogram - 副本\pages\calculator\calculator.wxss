/**calculator.wxss**/
@import '../../styles/design-system.wxss';

page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
}

/* 计算器表单 */
.calculator-form {
  margin: 30rpx 0;
}

.form-section {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  padding: 30rpx;
  background: var(--bg-tertiary);
  border-bottom: 1rpx solid var(--border-light);
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  flex: 0 0 160rpx;
}

.picker-value {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: var(--text-secondary);
  padding: 20rpx 24rpx;
  background: var(--bg-tertiary);
  border-radius: var(--rounded-sm);
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.picker-value:active {
  border-color: var(--primary-color);
  background: var(--bg-primary);
}

.picker-value.small {
  font-size: 24rpx;
  padding: 16rpx 20rpx;
}

/* 年龄输入区域 */
.age-inputs {
  flex: 1;
  margin-left: 20rpx;
}

.age-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.age-item:last-child {
  margin-bottom: 0;
}

.age-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  flex: 0 0 120rpx;
}

/* 输入框样式 */
.input-field {
  flex: 1;
  padding: 20rpx 24rpx;
  background: var(--bg-tertiary);
  border: 2rpx solid transparent;
  border-radius: var(--rounded-sm);
  font-size: 28rpx;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.input-field:focus {
  background: var(--bg-primary);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(74, 144, 226, 0.1);
}

.input-field::placeholder {
  color: var(--text-tertiary);
}

/* 计算按钮 */
.calculate-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  padding: 20rpx 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid var(--border-light);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.calculate-btn {
  width: 100%;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--rounded-base);
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: var(--shadow-primary);
  transition: all 0.2s ease;
}

.calculate-btn:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

.calculate-btn:disabled {
  background: var(--text-disabled);
  box-shadow: none;
  transform: none;
}

/* 结果显示 */
.result-section {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin: 30rpx 0;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: 2rpx solid var(--primary-color);
}

.result-header {
  background: var(--primary-gradient);
  color: white;
  padding: 30rpx;
  text-align: center;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.result-amount {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.result-period {
  font-size: 24rpx;
  opacity: 0.9;
}

.result-details {
  padding: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid var(--border-light);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.detail-value.highlight {
  color: var(--primary-color);
}

/* 说明文字 */
.note-section {
  background: rgba(74, 144, 226, 0.05);
  border-radius: var(--rounded-base);
  padding: 24rpx;
  margin: 20rpx 0;
  border-left: 4rpx solid var(--primary-color);
}

.note-title {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 12rpx;
}

.note-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}