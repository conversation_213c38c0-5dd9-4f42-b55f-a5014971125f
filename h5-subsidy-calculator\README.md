# 育儿补贴计算器

一个精美的H5应用，帮助用户计算中国育儿补贴政策下每月和总共可以领取的补贴金额。

## 功能特点

- 📱 **多设备完美适配** - 支持手机、平板、桌面等各种设备
- 🎨 **现代化UI界面** - 采用渐变色彩和卡片式设计
- 🧮 **精确计算逻辑** - 基于最新政策准确计算补贴金额
- 👶 **多孩子支持** - 支持1-10个孩子的补贴计算
- ⚡ **实时验证** - 输入数据实时验证，防止错误
- 📊 **详细结果展示** - 显示每个孩子的详细补贴信息
- 🔄 **PWA支持** - 可安装到主屏幕，支持离线使用
- 🌙 **深色模式** - 自动适配系统主题偏好
- ♿ **无障碍优化** - 支持高对比度和减少动画模式
- 👆 **触摸优化** - 专为触摸设备优化的交互体验

## 政策依据

根据中国最新育儿补贴政策：
- **实施时间**：2025年1月1日起
- **补贴对象**：3周岁以下婴幼儿
- **补贴标准**：每孩每年3600元（每月300元）
- **适用范围**：不分一孩、二孩、三孩

## 使用方法

1. 打开 `index.html` 文件
2. 输入孩子数量（1-10个）
3. 为每个孩子输入出生年月日
4. 点击"计算补贴"按钮
5. 查看详细的补贴计算结果

## 计算逻辑

### 补贴期间
- 从孩子出生日期开始（不早于2025年1月1日）
- 到孩子满3周岁为止

### 补贴金额
- 每月300元
- 按实际享受月数计算总金额

### 特殊情况处理
- 政策实施前已满3岁：无补贴
- 政策实施后出生：从出生开始计算
- 当前正在享受：显示当前每月补贴
- 已结束补贴：显示历史总金额

## 技术栈

- **HTML5** - 语义化标签和现代HTML特性
- **CSS3** - CSS变量、Grid布局、Flexbox、渐变等
- **JavaScript** - 原生ES6+，无依赖框架
- **响应式设计** - 移动端优先，适配各种屏幕尺寸

## 文件结构

```
h5-subsidy-calculator/
├── index.html          # 主页面
├── styles.css          # 样式文件 (包含完整响应式设计)
├── script.js           # 脚本文件 (包含移动端优化)
├── manifest.json       # PWA清单文件
├── sw.js              # Service Worker (离线支持)
├── test.html          # 测试页面
└── README.md          # 说明文档
```

## 多设备适配详情

### 响应式断点
- **超小屏幕** (≤375px): 手机竖屏优化
- **小屏幕** (376px-480px): 标准手机适配
- **中等屏幕** (481px-768px): 大手机/小平板
- **大屏幕** (769px-1024px): 平板设备
- **超大屏幕** (≥1025px): 桌面设备

### 移动端特性
- **触摸优化**: 44px最小触摸目标
- **防误触**: 双击缩放防护
- **输入优化**: 自动聚焦和滚动
- **安全区域**: iPhone X等刘海屏适配
- **PWA支持**: 可安装到主屏幕

### 无障碍支持
- **深色模式**: 自动检测系统偏好
- **高对比度**: 支持高对比度模式
- **减少动画**: 尊重用户动画偏好
- **语义化标签**: 屏幕阅读器友好

## 浏览器兼容性

### 桌面端
- Chrome 60+ ✅
- Firefox 55+ ✅
- Safari 12+ ✅
- Edge 79+ ✅

### 移动端
- iOS Safari 12+ ✅
- Chrome Mobile 60+ ✅
- Samsung Internet 8+ ✅
- Firefox Mobile 55+ ✅

### PWA支持
- Chrome/Edge (完整支持) ✅
- Safari (基础支持) ✅
- Firefox (基础支持) ✅

## 注意事项

1. **数据仅供参考** - 实际发放以当地政策为准
2. **日期限制** - 出生日期不能早于1990年1月1日，不能是未来日期
3. **政策更新** - 如政策有变化，需要更新计算逻辑
4. **补贴计算** - 按月计算，不足一个月的不计入补贴

## 开发说明

### 自定义样式
可以通过修改 `styles.css` 中的CSS变量来调整主题色彩：

```css
:root {
    --primary-color: #4f46e5;    /* 主色调 */
    --secondary-color: #10b981;  /* 辅助色 */
    --accent-color: #f59e0b;     /* 强调色 */
}
```

### 扩展功能
- 可以添加地区差异化政策支持
- 可以集成后端API进行数据存储
- 可以添加分享功能
- 可以添加打印功能

## 更新日志

### v1.0.2 (2025-01-01)
- 🐛 修复日期验证逻辑：移除不合理的日期限制，允许用户输入任何合理日期
- ✅ 优化用户体验：不再阻止用户输入2023年前的日期，而是正确计算并显示结果
- 🧮 改进计算逻辑：对于已超过3岁的孩子，显示"已超过3岁"状态而非错误
- 📝 添加更多测试用例验证各种边界情况

### v1.0.1 (2025-01-01)
- 🐛 修复日期验证逻辑相关问题
- 🐛 优化错误提示信息
- ✅ 添加测试用例
- 📝 更新文档说明

### v1.0.0 (2025-01-01)
- 初始版本发布
- 基础计算功能
- 响应式UI设计
- 多孩子支持
- PWA支持
- 多设备适配

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
