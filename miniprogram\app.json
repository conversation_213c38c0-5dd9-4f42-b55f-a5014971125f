{"pages": ["pages/index/index", "pages/policy/policy", "pages/development/development"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#4f46e5", "navigationBarTitleText": "育儿补贴计算器", "navigationBarTextStyle": "white", "backgroundColor": "#f8fafc", "enablePullDownRefresh": false}, "tabBar": {"color": "#6b7280", "selectedColor": "#4f46e5", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "计算器"}, {"pagePath": "pages/policy/policy", "text": "政策说明"}, {"pagePath": "pages/development/development", "text": "发育知识"}]}, "style": "v2", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}