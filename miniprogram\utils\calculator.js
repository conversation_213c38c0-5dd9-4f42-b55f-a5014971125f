// 补贴计算工具函数
const app = getApp()

/**
 * 计算单个孩子的补贴
 * @param {Object} child - 孩子信息
 * @param {Date} child.birthDate - 出生日期
 * @param {number} child.index - 孩子序号
 * @returns {Object} 计算结果
 */
function calculateChildSubsidy(child) {
  const config = app.globalData.policyConfig
  const now = new Date()
  const birthDate = new Date(child.birthDate)
  const thirdBirthday = new Date(birthDate)
  thirdBirthday.setFullYear(thirdBirthday.getFullYear() + config.maxAge)
  
  // 政策开始时间
  const policyStartDate = new Date(config.policyStartDate)
  
  // 确定补贴开始时间（出生日期和政策开始时间的较晚者）
  const subsidyStartDate = birthDate > policyStartDate ? birthDate : policyStartDate
  
  // 确定补贴结束时间（3岁生日）
  const subsidyEndDate = thirdBirthday
  
  // 如果孩子已经超过3岁，或者还没出生，则没有补贴
  if (now >= thirdBirthday || birthDate > now) {
    return {
      childIndex: child.index,
      birthDate: birthDate,
      monthlySubsidy: 0,
      totalSubsidy: 0,
      status: now >= thirdBirthday ? '已超过3岁' : '尚未出生',
      subsidyPeriod: '无补贴期间',
      totalMonths: 0
    }
  }
  
  // 如果政策还未开始且孩子已经3岁了
  if (subsidyStartDate >= subsidyEndDate) {
    return {
      childIndex: child.index,
      birthDate: birthDate,
      monthlySubsidy: 0,
      totalSubsidy: 0,
      status: '政策实施前已满3岁',
      subsidyPeriod: '无补贴期间',
      totalMonths: 0
    }
  }
  
  // 计算补贴月数（按完整月份计算）
  let totalMonths = 0
  let currentDate = new Date(subsidyStartDate)

  // 计算从开始日期到结束日期之间的完整月份
  while (currentDate < subsidyEndDate) {
    let nextMonth = new Date(currentDate)
    nextMonth.setMonth(nextMonth.getMonth() + 1)

    if (nextMonth <= subsidyEndDate) {
      totalMonths++
    } else {
      break
    }

    currentDate = nextMonth
  }
  
  // 计算金额
  const monthlyAmount = config.monthlyAmount
  const totalAmount = totalMonths * monthlyAmount
  
  // 判断当前状态
  let status = ''
  let currentMonthlySubsidy = 0
  
  if (now < subsidyStartDate) {
    status = '补贴尚未开始'
    currentMonthlySubsidy = 0
  } else if (now >= subsidyEndDate) {
    status = '补贴已结束'
    currentMonthlySubsidy = 0
  } else {
    status = '正在享受补贴'
    currentMonthlySubsidy = monthlyAmount
  }
  
  const subsidyPeriod = `${formatDate(subsidyStartDate)} 至 ${formatDate(subsidyEndDate)}`
  
  return {
    childIndex: child.index,
    birthDate: birthDate,
    monthlySubsidy: currentMonthlySubsidy,
    totalSubsidy: totalAmount,
    status: status,
    subsidyPeriod: subsidyPeriod,
    totalMonths: totalMonths
  }
}

/**
 * 计算多个孩子的补贴总计
 * @param {Array} children - 孩子信息数组
 * @returns {Object} 计算结果
 */
function calculateTotalSubsidy(children) {
  const results = children.map(child => calculateChildSubsidy(child))
  
  const totalMonthly = results.reduce((sum, result) => sum + result.monthlySubsidy, 0)
  const totalAmount = results.reduce((sum, result) => sum + result.totalSubsidy, 0)
  
  return {
    results,
    totalMonthly,
    totalAmount
  }
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 验证出生日期
 * @param {string} dateStr - 日期字符串
 * @returns {Object} 验证结果
 */
function validateBirthDate(dateStr) {
  if (!dateStr) {
    return { valid: false, message: '请选择出生日期' }
  }
  
  const birthDate = new Date(dateStr)
  const now = new Date()
  const minDate = new Date(app.globalData.policyConfig.minBirthDate)
  
  if (birthDate > now) {
    return { valid: false, message: '出生日期不能是未来日期' }
  }
  
  if (birthDate < minDate) {
    return { valid: false, message: '出生日期不能早于1990年1月1日' }
  }
  
  return { valid: true, message: '' }
}

module.exports = {
  calculateChildSubsidy,
  calculateTotalSubsidy,
  formatDate,
  validateBirthDate
}
