# 图标资源说明

此目录用于存放小程序所需的图标资源。

## 需要的图标文件

请准备以下图标文件：

### TabBar 图标
- `calculator.png` - 计算器图标（未选中状态）
- `calculator-active.png` - 计算器图标（选中状态）
- `policy.png` - 政策说明图标（未选中状态）
- `policy-active.png` - 政策说明图标（选中状态）

### 分享图标
- `share.png` - 分享时使用的图片

## 图标规格要求

### TabBar 图标
- 尺寸：81px × 81px
- 格式：PNG
- 背景：透明
- 颜色：未选中状态建议使用灰色，选中状态使用主题色

### 分享图标
- 尺寸：500px × 400px
- 格式：PNG/JPG
- 内容：包含小程序名称和简介

## 图标设计建议

1. **计算器图标**：可以使用计算器、算盘或数字相关的图标
2. **政策图标**：可以使用文档、公告或法规相关的图标
3. **整体风格**：建议采用简洁的线性图标风格，与小程序整体设计保持一致

## 临时解决方案

如果暂时没有图标资源，可以：
1. 使用微信开发者工具提供的默认图标
2. 从免费图标库下载合适的图标
3. 使用文字代替图标（需要修改 app.json 配置）

## 注意事项

- 所有图标都应该符合微信小程序的设计规范
- 图标文件大小建议控制在 40KB 以内
- 确保图标在不同设备上显示清晰
