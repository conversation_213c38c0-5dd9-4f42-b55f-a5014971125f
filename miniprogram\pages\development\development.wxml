<!--development.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="card">
      <view class="card-body text-center">
        <view class="card-title">
          <text class="icon">👶</text>
          <text>婴幼儿发育知识</text>
        </view>
        <text class="subtitle">3岁以内关键发育节点指南</text>
      </view>
    </view>
  </view>

  <!-- 年龄选择器 -->
  <view class="age-selector">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📅</text>
          <text>选择年龄段</text>
        </view>
      </view>
      <view class="card-body">
        <view class="age-tabs">
          <view 
            wx:for="{{ageGroups}}" 
            wx:key="id" 
            class="age-tab {{selectedAge === item.id ? 'active' : ''}}"
            bindtap="selectAge"
            data-age="{{item.id}}"
          >
            <text class="age-icon">{{item.icon}}</text>
            <text class="age-text">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 发育里程碑 -->
  <view class="milestones-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🎯</text>
          <text>{{currentAgeGroup.name}}发育里程碑</text>
        </view>
      </view>
      <view class="card-body">
        <view class="milestones-list">
          <view wx:for="{{currentMilestones}}" wx:key="category" class="milestone-category">
            <view class="category-header">
              <text class="category-icon">{{item.icon}}</text>
              <text class="category-title">{{item.category}}</text>
            </view>
            <view class="milestone-items">
              <view wx:for="{{item.items}}" wx:for-item="milestone" wx:key="*this" class="milestone-item">
                <text class="milestone-bullet">•</text>
                <text class="milestone-text">{{milestone}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 注意事项 -->
  <view class="notice-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">💡</text>
          <text>温馨提示</text>
        </view>
      </view>
      <view class="card-body">
        <view class="notice-list">
          <view class="notice-item">
            <text class="notice-icon">⚠️</text>
            <text class="notice-text">每个孩子的发育节奏不同，以上仅为参考标准</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">👨‍⚕️</text>
            <text class="notice-text">如有发育迟缓担忧，请及时咨询儿科医生</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">📚</text>
            <text class="notice-text">多与孩子互动，为其提供丰富的学习环境</text>
          </view>
          <view class="notice-item">
            <text class="notice-icon">❤️</text>
            <text class="notice-text">耐心陪伴，给予孩子充分的爱与支持</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 发育促进建议 -->
  <view class="suggestions-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">🌟</text>
          <text>发育促进建议</text>
        </view>
      </view>
      <view class="card-body">
        <view class="suggestions-list">
          <view wx:for="{{currentSuggestions}}" wx:key="category" class="suggestion-category">
            <view class="suggestion-header">
              <text class="suggestion-icon">{{item.icon}}</text>
              <text class="suggestion-title">{{item.category}}</text>
            </view>
            <view class="suggestion-content">
              <text class="suggestion-text">{{item.content}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关资源 -->
  <view class="resources-section">
    <view class="card">
      <view class="card-header">
        <view class="card-title">
          <text class="icon">📖</text>
          <text>相关资源</text>
        </view>
      </view>
      <view class="card-body">
        <view class="resources-list">
          <view class="resource-item" bindtap="openResource" data-type="book">
            <text class="resource-icon">📚</text>
            <view class="resource-content">
              <text class="resource-title">推荐育儿书籍</text>
              <text class="resource-desc">专业的婴幼儿发育指导书籍</text>
            </view>
          </view>
          <view class="resource-item" bindtap="openResource" data-type="app">
            <text class="resource-icon">📱</text>
            <view class="resource-content">
              <text class="resource-title">发育跟踪应用</text>
              <text class="resource-desc">记录宝宝成长里程碑</text>
            </view>
          </view>
          <view class="resource-item" bindtap="openResource" data-type="hospital">
            <text class="resource-icon">🏥</text>
            <view class="resource-content">
              <text class="resource-title">儿科医院</text>
              <text class="resource-desc">专业的儿童健康检查</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
