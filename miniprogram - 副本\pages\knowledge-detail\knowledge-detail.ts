// knowledge-detail.ts
Component({
  data: {
    articleId: '',
    article: {
      id: 1,
      title: '新生儿护理基础知识大全',
      banner: '/images/knowledge-banner-1.jpg',
      author: '李医生',
      authorAvatar: '/images/doctor-avatar.jpg',
      publishTime: '2024-01-15',
      viewCount: '1.2k',
      likeCount: '89',
      category: '新生儿护理',
      ageGroup: '0-1岁',
      content: `
        <h2>新生儿护理的重要性</h2>
        <p>新生儿期是宝宝生命中最脆弱也是最关键的时期。正确的护理方法不仅能保证宝宝的健康成长，还能为今后的发育打下良好的基础。</p>
        
        <h3>1. 喂养护理</h3>
        <p><strong>母乳喂养：</strong></p>
        <ul>
          <li>新生儿出生后应尽早开始母乳喂养，通常在出生后1小时内</li>
          <li>按需喂养，一般每2-3小时喂一次</li>
          <li>确保宝宝含乳姿势正确，避免乳头疼痛</li>
        </ul>
        
        <p><strong>人工喂养：</strong></p>
        <ul>
          <li>选择适合新生儿的配方奶粉</li>
          <li>严格按照说明书调配奶粉浓度</li>
          <li>奶瓶和奶嘴要彻底消毒</li>
        </ul>
        
        <h3>2. 睡眠护理</h3>
        <p>新生儿每天需要睡眠16-20小时，良好的睡眠环境对宝宝的发育至关重要：</p>
        <ul>
          <li>保持室温在22-26℃</li>
          <li>避免强光直射</li>
          <li>使用安全的睡眠姿势（仰卧位）</li>
          <li>床上用品要柔软、透气</li>
        </ul>
        
        <h3>3. 清洁护理</h3>
        <p><strong>洗澡：</strong></p>
        <ul>
          <li>脐带未脱落前可进行擦浴</li>
          <li>水温控制在37-40℃</li>
          <li>动作要轻柔，时间不宜过长</li>
        </ul>
        
        <p><strong>脐带护理：</strong></p>
        <ul>
          <li>保持脐部干燥清洁</li>
          <li>每天用75%酒精消毒</li>
          <li>观察是否有红肿、渗液等异常</li>
        </ul>
        
        <h3>4. 注意事项</h3>
        <p>在护理过程中，家长需要特别注意以下几点：</p>
        <ul>
          <li>观察宝宝的精神状态和食欲</li>
          <li>注意体温变化</li>
          <li>及时更换尿布，保持臀部清洁干燥</li>
          <li>定期体检，按时接种疫苗</li>
        </ul>
        
        <p>新生儿护理需要耐心和细心，遇到问题时不要慌张，及时咨询专业医生。随着经验的积累，家长们会越来越熟练，宝宝也会健康快乐地成长。</p>
      `
    },
    relatedArticles: [
      {
        id: 2,
        title: '新生儿常见疾病预防',
        summary: '了解新生儿常见疾病的症状和预防方法',
        thumbnail: '/images/knowledge-2.jpg',
        author: '张医生',
        publishTime: '3天前'
      },
      {
        id: 3,
        title: '母乳喂养的正确方法',
        summary: '详细介绍母乳喂养的技巧和注意事项',
        thumbnail: '/images/knowledge-3.jpg',
        author: '王护士',
        publishTime: '5天前'
      }
    ],
    isLiked: false,
    isFavorited: false
  },
  
  lifetimes: {
    attached() {
      // 获取页面参数
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const options = currentPage.options
      
      if (options.id) {
        this.setData({
          articleId: options.id
        })
        this.loadArticleDetail(options.id)
      }
    }
  },
  
  methods: {
    // 加载文章详情
    loadArticleDetail(id: string) {
      // 这里应该调用API获取文章详情
      // 暂时使用模拟数据
      console.log('加载文章详情:', id)
      
      // 模拟不同文章的内容
      const articles: { [key: string]: any } = {
        '1': {
          id: 1,
          title: '新生儿护理基础知识大全',
          banner: '/images/knowledge-banner-1.jpg',
          author: '李医生',
          authorAvatar: '/images/doctor-avatar.jpg',
          publishTime: '2024-01-15',
          viewCount: '1.2k',
          likeCount: '89',
          category: '新生儿护理',
          ageGroup: '0-1岁',
          content: this.data.article.content
        },
        '2': {
          id: 2,
          title: '幼儿早教启蒙的科学方法',
          banner: '/images/knowledge-banner-2.jpg',
          author: '王老师',
          authorAvatar: '/images/teacher-avatar.jpg',
          publishTime: '2024-01-12',
          viewCount: '856',
          likeCount: '67',
          category: '早期教育',
          ageGroup: '1-3岁',
          content: '<h2>早教的重要性</h2><p>早期教育对孩子的智力发育和性格形成具有重要影响...</p>'
        }
      }
      
      const article = articles[id] || this.data.article
      this.setData({
        article: article
      })
    },
    
    // 切换点赞状态
    toggleLike() {
      const isLiked = !this.data.isLiked
      this.setData({
        isLiked: isLiked
      })
      
      // 更新点赞数
      const likeCount = parseInt(this.data.article.likeCount.replace('k', '000').replace('.', ''))
      const newCount = isLiked ? likeCount + 1 : likeCount - 1
      const displayCount = newCount >= 1000 ? (newCount / 1000).toFixed(1) + 'k' : newCount.toString()
      
      this.setData({
        'article.likeCount': displayCount
      })
      
      wx.showToast({
        title: isLiked ? '点赞成功' : '取消点赞',
        icon: 'success',
        duration: 1000
      })
    },
    
    // 切换收藏状态
    toggleFavorite() {
      const isFavorited = !this.data.isFavorited
      this.setData({
        isFavorited: isFavorited
      })
      
      wx.showToast({
        title: isFavorited ? '收藏成功' : '取消收藏',
        icon: 'success',
        duration: 1000
      })
    },
    
    // 分享文章
    shareArticle() {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    },
    
    // 显示评论
    showComments() {
      wx.showToast({
        title: '评论功能开发中',
        icon: 'none'
      })
    },
    
    // 跳转到相关文章
    goToRelated(e: any) {
      const id = e.currentTarget.dataset.id
      wx.redirectTo({
        url: `/pages/knowledge-detail/knowledge-detail?id=${id}`
      })
    }
  }
})
