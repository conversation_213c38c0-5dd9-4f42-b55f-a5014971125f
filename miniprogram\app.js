// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null,
    // 政策配置
    policyConfig: {
      monthlyAmount: 300, // 每月补贴金额
      maxAge: 3, // 最大补贴年龄
      policyStartDate: '2025-01-01', // 政策开始时间
      minBirthDate: '1990-01-01' // 最早出生日期限制
    }
  }
})
