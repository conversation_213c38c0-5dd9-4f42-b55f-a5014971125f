/* CSS变量定义 */
:root {
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-card: #ffffff;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 480px;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
}

.header-content {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.icon {
    font-size: 2rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 0.95rem;
    font-weight: 400;
}

/* 政策信息卡片 */
.policy-info {
    margin-bottom: 1.5rem;
}

.policy-card {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--secondary-color);
}

.policy-card h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.policy-card ul {
    list-style: none;
}

.policy-card li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    position: relative;
    padding-left: 1.5rem;
}

.policy-card li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

/* 输入区域 */
.input-section {
    margin-bottom: 1.5rem;
}

.input-card {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.input-card h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.number-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 150px;
}

.number-input input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    text-align: center;
    transition: border-color 0.2s ease;
}

.number-input input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.btn-decrease,
.btn-increase {
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    background: var(--bg-primary);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-decrease:hover,
.btn-increase:hover {
    background: var(--primary-color);
    color: white;
}

.children-inputs {
    margin-bottom: 1.5rem;
}

.child-input {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
    border: 1px solid var(--border-light);
}

.child-input h4 {
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.child-input input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.child-input input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 日期输入框特殊样式 */
.child-input input[type="date"] {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--text-primary);
}

.child-input input[type="date"]::-webkit-calendar-picker-indicator {
    cursor: pointer;
    border-radius: 4px;
    margin-left: 8px;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.child-input input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* 修复某些浏览器的日期格式显示问题 */
.child-input input[type="date"]::-webkit-datetime-edit-text {
    color: var(--text-secondary);
}

.child-input input[type="date"]::-webkit-datetime-edit-month-field,
.child-input input[type="date"]::-webkit-datetime-edit-day-field,
.child-input input[type="date"]::-webkit-datetime-edit-year-field {
    color: var(--text-primary);
}

.calculate-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.calculate-btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 1.2rem;
}

/* 结果展示区域 */
.result-section {
    margin-bottom: 2rem;
}

.result-card {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.result-card h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.result-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-item {
    text-align: center;
    padding: 1.5rem 1rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.summary-item.monthly {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.summary-item.total {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.summary-label {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
}

.result-details {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
}

.child-result {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
}

.child-result:last-child {
    border-bottom: none;
}

.child-result h4 {
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.child-result p {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.result-note {
    background: #fef3c7;
    padding: 1rem;
    border-radius: var(--radius-md);
    border-left: 4px solid var(--accent-color);
}

.result-note p {
    color: #92400e;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.result-note p:last-child {
    margin-bottom: 0;
}

/* 底部 */
.footer {
    text-align: center;
    padding: 2rem 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

/* 响应式设计 */

/* 超小屏幕 (手机竖屏) */
@media (max-width: 375px) {
    .container {
        padding: 0.25rem;
        max-width: 100%;
    }

    .header-content {
        padding: 1rem;
        margin: 0.5rem;
    }

    .title {
        font-size: 1.25rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .icon {
        font-size: 1.5rem;
    }

    .subtitle {
        font-size: 0.8rem;
    }

    .policy-card,
    .input-card,
    .result-card {
        padding: 1rem;
        margin: 0.5rem;
    }

    .policy-card h3,
    .input-card h3,
    .result-card h3 {
        font-size: 1rem;
    }

    .policy-card li {
        font-size: 0.8rem;
        padding: 0.25rem 0;
    }

    .number-input {
        max-width: 120px;
    }

    .btn-decrease,
    .btn-increase {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .calculate-btn {
        padding: 0.75rem;
        font-size: 1rem;
    }

    .result-summary {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .summary-item {
        padding: 1rem 0.75rem;
    }

    .summary-value {
        font-size: 1.1rem;
    }

    .child-result {
        padding: 0.5rem 0;
    }

    .child-result h4 {
        font-size: 0.9rem;
    }

    .child-result p {
        font-size: 0.8rem;
    }
}

/* 小屏幕 (手机) */
@media (min-width: 376px) and (max-width: 480px) {
    .container {
        padding: 0.5rem;
    }

    .header-content {
        padding: 1.5rem;
    }

    .title {
        font-size: 1.5rem;
    }

    .result-summary {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .summary-value {
        font-size: 1.25rem;
    }
}

/* 中等屏幕 (大手机/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
    .container {
        padding: 1rem;
        max-width: 600px;
    }

    .header-content {
        padding: 2rem 1.5rem;
    }

    .title {
        font-size: 1.75rem;
    }

    .policy-card,
    .input-card,
    .result-card {
        padding: 1.5rem;
    }

    .result-summary {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .summary-item {
        padding: 1.25rem 1rem;
    }

    .summary-value {
        font-size: 1.4rem;
    }
}

/* 大屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 700px;
        padding: 1.5rem;
    }

    .header-content {
        padding: 2.5rem 2rem;
    }

    .title {
        font-size: 2rem;
    }

    .policy-card,
    .input-card,
    .result-card {
        padding: 2rem;
    }

    .result-summary {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }

    .summary-item {
        padding: 2rem 1.5rem;
    }

    .summary-value {
        font-size: 1.75rem;
    }
}

/* 超大屏幕 (桌面) */
@media (min-width: 1025px) {
    .container {
        max-width: 800px;
        padding: 2rem;
    }

    .header-content {
        padding: 3rem 2.5rem;
    }

    .title {
        font-size: 2.25rem;
    }

    .policy-card,
    .input-card,
    .result-card {
        padding: 2.5rem;
    }

    .result-summary {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .summary-item {
        padding: 2.5rem 2rem;
    }

    .summary-value {
        font-size: 2rem;
    }

    .child-result {
        padding: 1rem 0;
    }
}

/* 横屏适配 */
@media (max-height: 600px) and (orientation: landscape) {
    .header {
        margin-bottom: 1rem;
        padding: 1rem 0;
    }

    .header-content {
        padding: 1rem;
    }

    .title {
        font-size: 1.25rem;
    }

    .policy-info,
    .input-section,
    .result-section {
        margin-bottom: 1rem;
    }

    .policy-card,
    .input-card,
    .result-card {
        padding: 1rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn-decrease,
    .btn-increase,
    .calculate-btn {
        min-height: 44px; /* iOS推荐的最小触摸目标 */
    }

    .number-input input,
    .child-input input {
        min-height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
    }

    .calculate-btn {
        padding: 1rem;
        font-size: 1.1rem;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .header-content,
    .policy-card,
    .input-card,
    .result-card {
        box-shadow: var(--shadow-lg);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-section {
    animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading .calculate-btn {
    background: var(--text-light);
    cursor: not-allowed;
}

/* 移动端设备特定样式 */
.mobile-device .container {
    padding: 0.5rem;
}

.mobile-device .header-content,
.mobile-device .policy-card,
.mobile-device .input-card,
.mobile-device .result-card {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}

.mobile-device .calculate-btn {
    font-size: 1.1rem;
    padding: 1rem;
    border-radius: var(--radius-lg);
}

/* 触摸设备特定样式 */
.touch-device button {
    transition: transform 0.1s ease;
}

.touch-device button:active {
    transform: scale(0.95);
}

.touch-device .btn-decrease:active,
.touch-device .btn-increase:active {
    transform: scale(0.9);
}

/* 安全区域适配 (iPhone X等) */
@supports (padding: max(0px)) {
    .container {
        padding-left: max(1rem, env(safe-area-inset-left));
        padding-right: max(1rem, env(safe-area-inset-right));
        padding-bottom: max(1rem, env(safe-area-inset-bottom));
    }

    .header {
        padding-top: max(2rem, env(safe-area-inset-top));
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-light: #9ca3af;
        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --bg-card: #111827;
        --border-color: #374151;
        --border-light: #4b5563;
    }

    body {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .policy-card,
    .input-card,
    .result-card {
        background: var(--bg-card);
        border: 1px solid var(--border-color);
    }

    .result-note {
        background: #451a03;
        color: #fbbf24;
    }
}

/* 减少动画效果 (用户偏好) */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .result-section {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #006400;
        --accent-color: #ff8c00;
        --border-color: #000000;
    }

    .policy-card,
    .input-card,
    .result-card {
        border: 2px solid var(--border-color);
    }

    .calculate-btn {
        border: 2px solid #000000;
    }
}
