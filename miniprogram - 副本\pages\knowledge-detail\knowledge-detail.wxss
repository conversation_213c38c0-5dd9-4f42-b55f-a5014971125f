/**knowledge-detail.wxss**/
@import '../../styles/design-system.wxss';

page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
}

/* 文章头部 */
.article-header {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin: 30rpx 0;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.article-banner {
  width: 100%;
  height: 400rpx;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 24rpx;
}

.header-content {
  padding: 30rpx;
}

.article-title {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 24rpx;
}

.article-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.meta-left {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--bg-tertiary);
  margin-right: 16rpx;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.publish-time {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.meta-right {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
  color: var(--text-tertiary);
}

.stat-text {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.article-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 8rpx 16rpx;
  border-radius: var(--rounded-xl);
  font-size: 20rpx;
  font-weight: 500;
}

.category-tag {
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
}

.age-tag {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

/* 文章内容 */
.article-content {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: var(--shadow-light);
  line-height: 1.8;
  font-size: 28rpx;
  color: var(--text-primary);
}

/* 相关推荐 */
.related-section {
  background: var(--bg-primary);
  border-radius: var(--rounded-lg);
  margin: 20rpx 0;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  padding: 30rpx;
  background: var(--bg-tertiary);
  border-bottom: 1rpx solid var(--border-light);
}

.related-list {
  padding: 20rpx;
}

.related-item {
  display: flex;
  padding: 20rpx;
  border-radius: var(--rounded-base);
  margin-bottom: 16rpx;
  transition: background-color 0.2s ease;
}

.related-item:last-child {
  margin-bottom: 0;
}

.related-item:active {
  background: var(--bg-tertiary);
}

.related-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--rounded-sm);
  background: var(--bg-tertiary);
  margin-right: 20rpx;
  flex-shrink: 0;
}

.related-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.related-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-summary {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.related-author {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

.related-time {
  font-size: 22rpx;
  color: var(--text-tertiary);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  padding: 20rpx 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid var(--border-light);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-around;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  border-radius: var(--rounded-base);
  transition: all 0.2s ease;
  min-width: 120rpx;
}

.action-item:active {
  background: var(--bg-tertiary);
  transform: scale(0.95);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  transition: all 0.2s ease;
}

.action-icon.liked {
  color: var(--error-color);
}

.action-icon.favorited {
  color: var(--warning-color);
}

.action-text {
  font-size: 22rpx;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.action-item:active .action-text {
  color: var(--primary-color);
}

.comment-btn {
  background: var(--primary-color);
  color: white;
}

.comment-btn .action-icon,
.comment-btn .action-text {
  color: white;
}