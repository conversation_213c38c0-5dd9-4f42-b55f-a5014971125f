/**app.scss**/
@import './styles/design-system.wxss';

/* 全局样式重置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-secondary);
}

/* 通用容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局按钮样式 */
button {
  border: none;
  outline: none;
  background: transparent;
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
  line-height: inherit;
}

button::after {
  border: none;
}

/* 全局输入框样式 */
input, textarea {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
}

/* 全局图片样式 */
image {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 全局文本样式 */
text {
  font-family: inherit;
  color: inherit;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* 底部标签栏样式优化 */
.tab-bar {
  background: var(--bg-primary);
  border-top: 1rpx solid var(--border-light);
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.tab-bar-item {
  color: var(--text-tertiary);
  font-size: 20rpx;
  transition: all 0.2s ease;
}

.tab-bar-item.selected {
  color: var(--primary-color);
}

/* 导航栏样式优化 */
.navigation-bar {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 2rpx 16rpx rgba(74, 144, 226, 0.2);
}

/* 通用动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.hidden {
  display: none;
}

.visible {
  display: block;
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}

.safe-area-top {
  padding-top: calc(env(safe-area-inset-top) + 20rpx);
}

/* 响应式断点 */
@media (max-width: 375px) {
  page {
    font-size: 26rpx;
  }

  .container {
    padding: 160rpx 0;
  }
}

@media (min-width: 414px) {
  page {
    font-size: 30rpx;
  }
}
