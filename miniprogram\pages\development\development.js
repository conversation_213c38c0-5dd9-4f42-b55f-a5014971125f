// development.js
Page({
  data: {
    selectedAge: '0-6months',
    ageGroups: [
      { id: '0-6months', name: '0-6个月', icon: '🍼' },
      { id: '6-12months', name: '6-12个月', icon: '👶' },
      { id: '1-2years', name: '1-2岁', icon: '🚶' },
      { id: '2-3years', name: '2-3岁', icon: '🏃' }
    ],
    currentAgeGroup: {},
    currentMilestones: [],
    currentSuggestions: [],

    // 发育里程碑数据
    milestonesData: {
      '0-6months': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '2个月：能抬头45度',
            '3个月：俯卧时能抬头90度',
            '4个月：能翻身（从仰卧到侧卧）',
            '5个月：能坐立（需要支撑）',
            '6个月：能独立坐立片刻'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '2个月：能注视人脸和物体',
            '3个月：能追视移动物体',
            '4个月：对声音有反应',
            '5个月：能认识熟悉的人',
            '6个月：开始有陌生人焦虑'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '2个月：能发出"啊"、"呜"等声音',
            '3个月：会笑出声',
            '4个月：能发出更多元音',
            '5个月：开始咿呀学语',
            '6个月：能发出"ba"、"ma"等音节'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '2个月：会对人微笑',
            '3个月：喜欢与人互动',
            '4个月：会主动寻求关注',
            '5个月：能表达喜怒情绪',
            '6个月：开始模仿简单动作'
          ]
        }
      ],
      '6-12months': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '7个月：能独立坐稳',
            '8个月：开始爬行',
            '9个月：能扶物站立',
            '10个月：能扶物行走',
            '11个月：能独立站立',
            '12个月：开始独立行走'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '7个月：有物体永恒概念',
            '8个月：能理解简单指令',
            '9个月：会玩躲猫猫游戏',
            '10个月：能模仿动作',
            '11个月：理解"不"的含义',
            '12个月：能指认身体部位'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '7个月：能发出双音节',
            '8个月：开始理解词汇',
            '9个月：会说"mama"、"baba"',
            '10个月：能模仿声音',
            '11个月：理解简单词汇',
            '12个月：能说1-2个有意义的词'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '7个月：开始有分离焦虑',
            '8个月：喜欢与其他婴儿互动',
            '9个月：会挥手再见',
            '10个月：喜欢模仿大人',
            '11个月：开始有合作行为',
            '12个月：能表达基本需求'
          ]
        }
      ],
      '1-2years': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '15个月：能独立行走稳定',
            '18个月：能跑步和爬楼梯',
            '21个月：能踢球',
            '24个月：能双脚跳跃'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '15个月：能完成简单拼图',
            '18个月：开始有想象力游戏',
            '21个月：能分类物品',
            '24个月：理解因果关系'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '15个月：词汇量达到10-20个',
            '18个月：能说50个词汇',
            '21个月：开始组合词汇',
            '24个月：能说2-3个词的句子'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '15个月：喜欢独立探索',
            '18个月：开始有自我意识',
            '21个月：能表达情感需求',
            '24个月：开始与同龄人互动'
          ]
        }
      ],
      '2-3years': [
        {
          category: '运动发育',
          icon: '🤸',
          items: [
            '30个月：能骑三轮车',
            '33个月：能单脚站立',
            '36个月：能接球和投球'
          ]
        },
        {
          category: '认知发育',
          icon: '🧠',
          items: [
            '30个月：能数到3',
            '33个月：理解大小概念',
            '36个月：能完成复杂拼图'
          ]
        },
        {
          category: '语言发育',
          icon: '🗣️',
          items: [
            '30个月：词汇量达到300个',
            '33个月：能说完整句子',
            '36个月：能讲简单故事'
          ]
        },
        {
          category: '社交发育',
          icon: '👥',
          items: [
            '30个月：开始学会分享',
            '33个月：能表达复杂情感',
            '36个月：喜欢角色扮演游戏'
          ]
        }
      ]
    },

    // 发育促进建议数据
    suggestionsData: {
      '0-6months': [
        {
          category: '感官刺激',
          icon: '👁️',
          content: '多与宝宝进行眼神交流，使用黑白对比强烈的图片刺激视觉发育。播放轻柔音乐，促进听觉发展。'
        },
        {
          category: '运动训练',
          icon: '🏃',
          content: '每天进行俯卧练习，帮助宝宝练习抬头。轻柔按摩四肢，促进肌肉发育。'
        },
        {
          category: '语言启蒙',
          icon: '💬',
          content: '经常与宝宝说话，描述正在做的事情。重复宝宝发出的声音，鼓励语言发展。'
        },
        {
          category: '情感交流',
          icon: '❤️',
          content: '及时回应宝宝的需求，建立安全感。多拥抱和抚摸，促进亲子关系。'
        }
      ],
      '6-12months': [
        {
          category: '探索环境',
          icon: '🔍',
          content: '提供安全的爬行空间，鼓励宝宝探索。准备不同质地的玩具，刺激触觉发育。'
        },
        {
          category: '精细动作',
          icon: '✋',
          content: '提供适合抓握的小物品，练习手指精细动作。教宝宝用勺子吃饭，培养自理能力。'
        },
        {
          category: '认知游戏',
          icon: '🧩',
          content: '玩躲猫猫游戏，发展物体永恒概念。教宝宝简单的手势，如挥手再见。'
        },
        {
          category: '社交互动',
          icon: '👨‍👩‍👧‍👦',
          content: '安排与其他宝宝的互动时间。建立规律的日常作息，培养安全感。'
        }
      ],
      '1-2years': [
        {
          category: '语言发展',
          icon: '📚',
          content: '每天读绘本给宝宝听，丰富词汇量。鼓励宝宝表达需求，耐心倾听。'
        },
        {
          category: '独立能力',
          icon: '🚶',
          content: '鼓励宝宝自己吃饭、穿衣。提供选择机会，培养决策能力。'
        },
        {
          category: '创造力',
          icon: '🎨',
          content: '提供画笔和纸张，鼓励涂鸦。进行简单的手工活动，发展创造力。'
        },
        {
          category: '规则意识',
          icon: '📏',
          content: '建立简单的家庭规则，培养纪律性。用正面语言引导行为。'
        }
      ],
      '2-3years': [
        {
          category: '学习准备',
          icon: '🎓',
          content: '教授基本的数字和字母。进行分类和配对游戏，发展逻辑思维。'
        },
        {
          category: '社交技能',
          icon: '🤝',
          content: '教宝宝分享和轮流。安排与同龄人的玩耍时间，学习社交技能。'
        },
        {
          category: '情绪管理',
          icon: '😊',
          content: '帮助宝宝识别和表达情绪。教授简单的情绪调节方法。'
        },
        {
          category: '生活技能',
          icon: '🏠',
          content: '让宝宝参与简单的家务活动。培养基本的自理能力和责任感。'
        }
      ]
    }
  },

  onLoad() {
    this.initializeData()
  },

  // 初始化数据
  initializeData() {
    const currentAge = this.data.ageGroups.find(group => group.id === this.data.selectedAge)
    this.setData({
      currentAgeGroup: currentAge,
      currentMilestones: this.data.milestonesData[this.data.selectedAge],
      currentSuggestions: this.data.suggestionsData[this.data.selectedAge]
    })
  },

  // 选择年龄段
  selectAge(e) {
    const ageId = e.currentTarget.dataset.age
    const currentAge = this.data.ageGroups.find(group => group.id === ageId)

    this.setData({
      selectedAge: ageId,
      currentAgeGroup: currentAge,
      currentMilestones: this.data.milestonesData[ageId],
      currentSuggestions: this.data.suggestionsData[ageId]
    })
  },

  // 打开资源
  openResource(e) {
    const type = e.currentTarget.dataset.type
    let message = ''

    switch(type) {
      case 'book':
        message = '推荐书籍：《美国儿科学会育儿百科》、《西尔斯亲密育儿百科》等'
        break
      case 'app':
        message = '推荐应用：宝宝树、育儿宝等专业育儿应用'
        break
      case 'hospital':
        message = '建议定期到儿童医院进行健康检查和发育评估'
        break
    }

    wx.showModal({
      title: '资源推荐',
      content: message,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '婴幼儿发育知识 - 关键发育节点指南',
      path: '/pages/development/development'
    }
  },

  onShareTimeline() {
    return {
      title: '婴幼儿发育知识 - 关键发育节点指南'
    }
  }
})