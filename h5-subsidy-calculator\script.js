// 全局变量
let childCount = 1;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateChildInputs();
    initializeMobileOptimizations();
    registerServiceWorker();
});

// 注册Service Worker
function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('./sw.js')
                .then(function(registration) {
                    console.log('Service Worker 注册成功:', registration.scope);

                    // 检查更新
                    registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', function() {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // 新版本可用
                                if (confirm('发现新版本，是否立即更新？')) {
                                    window.location.reload();
                                }
                            }
                        });
                    });
                })
                .catch(function(error) {
                    console.log('Service Worker 注册失败:', error);
                });
        });
    }
}

// 移动端优化初始化
function initializeMobileOptimizations() {
    // 防止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);

    // 优化输入框在移动端的体验
    const inputs = document.querySelectorAll('input[type="date"], input[type="number"]');
    inputs.forEach(input => {
        // 移动端输入框获得焦点时滚动到视图中
        input.addEventListener('focus', function() {
            setTimeout(() => {
                this.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
        });

        // 移动端数字输入优化
        if (input.type === 'number') {
            input.addEventListener('input', function() {
                // 限制输入范围
                if (this.value < 1) this.value = 1;
                if (this.value > 10) this.value = 10;
            });
        }
    });

    // 添加触摸反馈
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });

        button.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });

        button.addEventListener('touchcancel', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // 检测设备类型并添加相应的类
    if (isMobileDevice()) {
        document.body.classList.add('mobile-device');
    }

    if (isTouchDevice()) {
        document.body.classList.add('touch-device');
    }
}

// 检测是否为移动设备
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 检测是否为触摸设备
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// 增加孩子数量
function increaseChildCount() {
    if (childCount < 10) {
        childCount++;
        document.getElementById('childCount').value = childCount;
        updateChildInputs();
    }
}

// 减少孩子数量
function decreaseChildCount() {
    if (childCount > 1) {
        childCount--;
        document.getElementById('childCount').value = childCount;
        updateChildInputs();
    }
}

// 更新孩子输入框
function updateChildInputs() {
    const childCountInput = document.getElementById('childCount');
    childCount = parseInt(childCountInput.value) || 1;
    
    // 限制范围
    if (childCount < 1) {
        childCount = 1;
        childCountInput.value = 1;
    }
    if (childCount > 10) {
        childCount = 10;
        childCountInput.value = 10;
    }
    
    const container = document.getElementById('childrenInputs');
    container.innerHTML = '';
    
    for (let i = 1; i <= childCount; i++) {
        const childDiv = document.createElement('div');
        childDiv.className = 'child-input';
        childDiv.innerHTML = `
            <h4>👶 第${i}个孩子</h4>
            <label for="child${i}Birth">出生日期：</label>
            <input type="date" id="child${i}Birth" name="child${i}Birth" required placeholder="yyyy-mm-dd" title="请选择出生日期，格式：年-月-日">
        `;
        container.appendChild(childDiv);
    }
}

// 计算补贴
function calculateSubsidy() {
    // 添加加载状态
    const calculateBtn = document.querySelector('.calculate-btn');
    const originalText = calculateBtn.innerHTML;
    calculateBtn.innerHTML = '<span class="btn-icon">⏳</span>计算中...';
    calculateBtn.disabled = true;
    calculateBtn.classList.add('loading');

    // 使用setTimeout来显示加载状态
    setTimeout(() => {
        try {
            // 获取所有孩子的出生日期
            const children = [];
            let hasError = false;
            let errorMessage = '';

            for (let i = 1; i <= childCount; i++) {
                const birthInput = document.getElementById(`child${i}Birth`);
                const birthDate = birthInput.value;

                if (!birthDate) {
                    errorMessage = `请填写第${i}个孩子的出生日期`;
                    showMobileAlert(errorMessage);
                    birthInput.focus();
                    hasError = true;
                    break;
                }

                const birth = new Date(birthDate);
                const now = new Date();

                // 验证日期不能是未来
                if (birth > now) {
                    errorMessage = `第${i}个孩子的出生日期不能是未来日期`;
                    showMobileAlert(errorMessage);
                    birthInput.focus();
                    hasError = true;
                    break;
                }

                // 验证日期不能太早（设置一个合理的最早日期，比如不能早于1990年）
                const minDate = new Date('1990-01-01');
                if (birth < minDate) {
                    errorMessage = `第${i}个孩子的出生日期不能早于1990年1月1日`;
                    showMobileAlert(errorMessage);
                    birthInput.focus();
                    hasError = true;
                    break;
                }

                children.push({
                    index: i,
                    birthDate: birth
                });
            }

            if (hasError) {
                return;
            }

            // 计算每个孩子的补贴
            const results = children.map(child => calculateChildSubsidy(child));

            // 计算总计
            const totalMonthly = results.reduce((sum, result) => sum + result.monthlySubsidy, 0);
            const totalAmount = results.reduce((sum, result) => sum + result.totalSubsidy, 0);

            // 显示结果
            displayResults(results, totalMonthly, totalAmount);

            // 添加成功提示
            if (isMobileDevice()) {
                showMobileToast('计算完成！');
            }

        } catch (error) {
            console.error('计算过程中出现错误:', error);
            showMobileAlert('计算过程中出现错误，请重试');
        } finally {
            // 恢复按钮状态
            calculateBtn.innerHTML = originalText;
            calculateBtn.disabled = false;
            calculateBtn.classList.remove('loading');
        }
    }, 300); // 短暂延迟以显示加载状态
}

// 移动端友好的提示框
function showMobileAlert(message) {
    if (isMobileDevice()) {
        // 创建自定义提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = 'mobile-alert';
        alertDiv.innerHTML = `
            <div class="mobile-alert-content">
                <p>${message}</p>
                <button onclick="this.parentElement.parentElement.remove()">确定</button>
            </div>
        `;
        document.body.appendChild(alertDiv);

        // 添加样式
        if (!document.getElementById('mobile-alert-styles')) {
            const style = document.createElement('style');
            style.id = 'mobile-alert-styles';
            style.textContent = `
                .mobile-alert {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    padding: 1rem;
                }
                .mobile-alert-content {
                    background: white;
                    padding: 1.5rem;
                    border-radius: 12px;
                    max-width: 300px;
                    text-align: center;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                }
                .mobile-alert-content p {
                    margin: 0 0 1rem 0;
                    color: #333;
                    line-height: 1.5;
                }
                .mobile-alert-content button {
                    background: #4f46e5;
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 8px;
                    font-size: 1rem;
                    cursor: pointer;
                    min-width: 80px;
                }
            `;
            document.head.appendChild(style);
        }
    } else {
        alert(message);
    }
}

// 移动端Toast提示
function showMobileToast(message) {
    const toast = document.createElement('div');
    toast.className = 'mobile-toast';
    toast.textContent = message;
    document.body.appendChild(toast);

    // 添加样式
    if (!document.getElementById('mobile-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-toast-styles';
        style.textContent = `
            .mobile-toast {
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                font-size: 14px;
                z-index: 10000;
                animation: toastSlideUp 0.3s ease-out;
            }
            @keyframes toastSlideUp {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 自动移除
    setTimeout(() => {
        toast.style.animation = 'toastSlideUp 0.3s ease-out reverse';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// 计算单个孩子的补贴
function calculateChildSubsidy(child) {
    const now = new Date();
    const birthDate = child.birthDate;
    const thirdBirthday = new Date(birthDate);
    thirdBirthday.setFullYear(thirdBirthday.getFullYear() + 3);
    
    // 政策开始时间：2025年1月1日
    const policyStartDate = new Date('2025-01-01');
    
    // 确定补贴开始时间（出生日期和政策开始时间的较晚者）
    const subsidyStartDate = birthDate > policyStartDate ? birthDate : policyStartDate;
    
    // 确定补贴结束时间（3岁生日）
    const subsidyEndDate = thirdBirthday;
    
    // 如果孩子已经超过3岁，或者还没出生，则没有补贴
    if (now >= thirdBirthday || birthDate > now) {
        return {
            childIndex: child.index,
            birthDate: birthDate,
            monthlySubsidy: 0,
            totalSubsidy: 0,
            status: now >= thirdBirthday ? '已超过3岁' : '尚未出生',
            subsidyPeriod: '无补贴期间'
        };
    }
    
    // 如果政策还未开始且孩子已经3岁了
    if (subsidyStartDate >= subsidyEndDate) {
        return {
            childIndex: child.index,
            birthDate: birthDate,
            monthlySubsidy: 0,
            totalSubsidy: 0,
            status: '政策实施前已满3岁',
            subsidyPeriod: '无补贴期间'
        };
    }
    
    // 计算补贴月数（按完整月份计算）
    let totalMonths = 0;
    let currentDate = new Date(subsidyStartDate);

    // 计算从开始日期到结束日期之间的完整月份
    while (currentDate < subsidyEndDate) {
        let nextMonth = new Date(currentDate);
        nextMonth.setMonth(nextMonth.getMonth() + 1);

        if (nextMonth <= subsidyEndDate) {
            totalMonths++;
        } else {
            // 检查是否还有不足一个月的时间
            break;
        }

        currentDate = nextMonth;
    }
    
    // 每月300元
    const monthlyAmount = 300;
    const totalAmount = totalMonths * monthlyAmount;
    
    // 判断当前状态
    let status = '';
    let currentMonthlySubsidy = 0;
    
    if (now < subsidyStartDate) {
        status = '补贴尚未开始';
        currentMonthlySubsidy = 0;
    } else if (now >= subsidyEndDate) {
        status = '补贴已结束';
        currentMonthlySubsidy = 0;
    } else {
        status = '正在享受补贴';
        currentMonthlySubsidy = monthlyAmount;
    }
    
    const subsidyPeriod = `${formatDate(subsidyStartDate)} 至 ${formatDate(subsidyEndDate)}`;
    
    return {
        childIndex: child.index,
        birthDate: birthDate,
        monthlySubsidy: currentMonthlySubsidy,
        totalSubsidy: totalAmount,
        status: status,
        subsidyPeriod: subsidyPeriod,
        totalMonths: totalMonths
    };
}

// 显示计算结果
function displayResults(results, totalMonthly, totalAmount) {
    // 显示总计
    document.getElementById('monthlySubsidy').textContent = `¥${totalMonthly.toLocaleString()}`;
    document.getElementById('totalSubsidy').textContent = `¥${totalAmount.toLocaleString()}`;
    
    // 显示详细结果
    const detailsContainer = document.getElementById('resultDetails');
    detailsContainer.innerHTML = '';
    
    results.forEach(result => {
        const childDiv = document.createElement('div');
        childDiv.className = 'child-result';
        
        const statusColor = result.monthlySubsidy > 0 ? '#10b981' : '#6b7280';
        
        childDiv.innerHTML = `
            <h4>👶 第${result.childIndex}个孩子</h4>
            <p><strong>出生日期：</strong>${formatDate(result.birthDate)}</p>
            <p><strong>补贴期间：</strong>${result.subsidyPeriod}</p>
            <p><strong>当前状态：</strong><span style="color: ${statusColor}; font-weight: 600;">${result.status}</span></p>
            <p><strong>每月补贴：</strong>¥${result.monthlySubsidy.toLocaleString()}</p>
            <p><strong>总补贴：</strong>¥${result.totalSubsidy.toLocaleString()}</p>
            ${result.totalMonths ? `<p><strong>补贴月数：</strong>${result.totalMonths}个月</p>` : ''}
        `;
        
        detailsContainer.appendChild(childDiv);
    });
    
    // 显示结果区域
    const resultSection = document.getElementById('resultSection');
    resultSection.style.display = 'block';
    
    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

// 格式化日期
function formatDate(date) {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

// 添加输入验证
document.addEventListener('input', function(e) {
    if (e.target.type === 'date') {
        const now = new Date();
        const inputDate = new Date(e.target.value);
        
        if (inputDate > now) {
            e.target.setCustomValidity('出生日期不能是未来日期');
        } else if (inputDate < new Date('1990-01-01')) {
            e.target.setCustomValidity('出生日期不能早于1990年1月1日');
        } else {
            e.target.setCustomValidity('');
        }
    }
});

// 添加键盘事件支持
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        const activeElement = document.activeElement;
        if (activeElement.type === 'date' || activeElement.type === 'number') {
            calculateSubsidy();
        }
    }
});

// 添加数字输入框的事件监听
document.getElementById('childCount').addEventListener('input', function(e) {
    const value = parseInt(e.target.value);
    if (value >= 1 && value <= 10) {
        childCount = value;
        updateChildInputs();
    }
});
