<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="育儿补贴计算器">
    <meta name="theme-color" content="#4f46e5">
    <meta name="description" content="精美的H5育儿补贴计算器，帮助您计算每月和总共可领取的育儿补贴金额">
    <meta name="keywords" content="育儿补贴,补贴计算器,H5应用,移动端">

    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%234f46e5'/><text x='50' y='60' font-size='40' text-anchor='middle' fill='white'>👶</text></svg>">

    <!-- Android图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%234f46e5'/><text x='50' y='60' font-size='40' text-anchor='middle' fill='white'>👶</text></svg>">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <title>育儿补贴计算器</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <span class="icon">👶</span>
                    育儿补贴计算器
                </h1>
                <p class="subtitle">计算您的家庭每月和总共可领取的育儿补贴</p>
            </div>
        </header>

        <!-- 政策说明 -->
        <section class="policy-info">
            <div class="policy-card">
                <h3>📋 政策说明</h3>
                <ul>
                    <li>补贴对象：3周岁以下婴幼儿</li>
                    <li>补贴标准：每孩每年3600元（每月300元）</li>
                    <li>适用范围：不分一孩、二孩、三孩</li>
                    <li>实施时间：2025年1月1日起</li>
                </ul>
            </div>
        </section>

        <!-- 输入区域 -->
        <section class="input-section">
            <div class="input-card">
                <h3>👨‍👩‍👧‍👦 家庭信息</h3>
                
                <div class="form-group">
                    <label for="childCount">孩子数量：</label>
                    <div class="number-input">
                        <button type="button" class="btn-decrease" onclick="decreaseChildCount()">-</button>
                        <input type="number" id="childCount" value="1" min="1" max="10" onchange="updateChildInputs()">
                        <button type="button" class="btn-increase" onclick="increaseChildCount()">+</button>
                    </div>
                </div>

                <div id="childrenInputs" class="children-inputs">
                    <!-- 动态生成的孩子信息输入框 -->
                </div>

                <button class="calculate-btn" onclick="calculateSubsidy()">
                    <span class="btn-icon">🧮</span>
                    计算补贴
                </button>
            </div>
        </section>

        <!-- 结果展示区域 -->
        <section class="result-section" id="resultSection" style="display: none;">
            <div class="result-card">
                <h3>💰 补贴计算结果</h3>
                
                <div class="result-summary">
                    <div class="summary-item monthly">
                        <div class="summary-label">每月补贴</div>
                        <div class="summary-value" id="monthlySubsidy">¥0</div>
                    </div>
                    <div class="summary-item total">
                        <div class="summary-label">总补贴金额</div>
                        <div class="summary-value" id="totalSubsidy">¥0</div>
                    </div>
                </div>

                <div class="result-details" id="resultDetails">
                    <!-- 详细结果将在这里显示 -->
                </div>

                <div class="result-note">
                    <p>💡 <strong>温馨提示：</strong></p>
                    <p>• 补贴从孩子出生开始计算，直至满3周岁</p>
                    <p>• 实际发放以当地政策为准</p>
                    <p>• 建议及时关注当地相关部门通知</p>
                </div>
            </div>
        </section>

        <!-- 底部 -->
        <footer class="footer">
            <p>© 2025 育儿补贴计算器 | 数据仅供参考</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
