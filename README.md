# 育儿补贴计算助手

一个专为新手父母设计的微信小程序，提供准确的育儿补贴计算和实用的育儿知识。

## 项目概述

### 核心功能
- **补贴计算器**: 根据地区、收入、孩子数量等参数计算各类育儿补贴
- **育儿知识库**: 分龄段育儿知识、政策解读、专家建议
- **政策资讯**: 最新育儿政策动态和补贴变化通知
- **个人中心**: 用户信息管理、计算历史、收藏功能

### 技术架构

#### 前端技术栈
- **开发框架**: 微信小程序原生开发
- **UI组件**: WeUI + 自定义组件
- **状态管理**: 小程序原生状态管理
- **样式预处理**: SCSS
- **开发语言**: TypeScript

#### 后端技术栈（推荐）
- **云服务**: 微信云开发
- **数据库**: 云数据库 MongoDB
- **云函数**: Node.js
- **文件存储**: 云存储

## 项目结构

```
miniprogram/
├── pages/                  # 页面目录
│   ├── index/              # 首页
│   ├── calculator/         # 补贴计算页面
│   ├── knowledge/          # 育儿知识列表
│   ├── knowledge-detail/   # 知识详情页面
│   ├── policy-news/        # 政策资讯
│   ├── news-detail/        # 资讯详情
│   └── profile/            # 个人中心
├── components/             # 组件目录
│   └── navigation-bar/     # 自定义导航栏
├── utils/                  # 工具函数
├── images/                 # 图片资源
├── app.json               # 小程序配置
├── app.scss               # 全局样式
└── app.ts                 # 小程序入口
```

## 功能特性

### 1. 智能补贴计算
- 支持多地区政策差异
- 考虑家庭收入、就业状态、社保情况
- 实时计算生育津贴、育儿补贴、托育补贴等
- 提供详细的申请指南

### 2. 分龄段育儿知识
- **0-1岁**: 新生儿护理、喂养指南、疫苗接种
- **1-3岁**: 幼儿发育、早教启蒙、安全防护
- **3-6岁**: 学前教育、习惯养成、社交能力
- **专家问答**: 常见育儿问题解答

### 3. 政策资讯推送
- 最新育儿政策解读
- 补贴标准变化通知
- 申请流程更新提醒
- 地区性政策差异说明

### 4. 个性化服务
- 用户信息管理
- 计算历史记录
- 知识收藏功能
- 个性化推荐

## 开发进度

- [x] 项目规划与需求分析
- [x] 技术架构设计
- [x] 产品功能设计
- [x] UI/UX设计
- [x] 前端开发
- [ ] 后端开发
- [ ] 测试与优化
- [ ] 部署与发布

## 安装和运行

### 环境要求
- 微信开发者工具
- Node.js 16+
- 微信小程序开发账号

### 本地开发
1. 克隆项目到本地
2. 使用微信开发者工具打开项目
3. 配置小程序AppID
4. 点击编译运行

### 云开发配置（可选）
1. 在微信开发者工具中开通云开发
2. 配置云函数和数据库
3. 部署云函数
4. 配置数据库权限

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 微信群: 扫描小程序内二维码加入

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 基础补贴计算功能
- 育儿知识库
- 用户系统

---

**注意**: 本项目仅供学习和参考使用，实际补贴政策请以当地政府部门发布的官方信息为准。
