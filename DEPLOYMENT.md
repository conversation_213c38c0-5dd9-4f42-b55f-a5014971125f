# 部署指南

本文档详细说明了如何部署育儿补贴计算助手微信小程序。

## 前置条件

1. **微信小程序账号**
   - 已注册的微信小程序账号
   - 获取小程序的 AppID

2. **开发工具**
   - 微信开发者工具（最新版本）
   - Node.js 16+ 环境

3. **云开发环境**
   - 开通微信云开发服务
   - 创建云开发环境

## 部署步骤

### 1. 项目配置

#### 1.1 配置 AppID
在 `miniprogram/project.config.json` 中配置你的小程序 AppID：

```json
{
  "appid": "你的小程序AppID",
  "projectname": "育儿补贴计算助手"
}
```

#### 1.2 云开发配置
在微信开发者工具中：
1. 点击工具栏中的"云开发"
2. 开通云开发服务
3. 创建环境（建议创建测试和生产两个环境）

### 2. 云函数部署

#### 2.1 部署计算补贴云函数
```bash
# 右键点击 cloudfunctions/calculateSubsidy 文件夹
# 选择"上传并部署：云端安装依赖"
```

#### 2.2 部署获取知识云函数
```bash
# 右键点击 cloudfunctions/getKnowledge 文件夹
# 选择"上传并部署：云端安装依赖"
```

### 3. 数据库初始化

#### 3.1 创建数据库集合
在云开发控制台中创建以下集合：

1. **knowledge_articles** - 育儿知识文章
2. **calculation_history** - 计算历史记录
3. **user_reading_history** - 用户阅读历史
4. **policy_news** - 政策资讯
5. **user_profiles** - 用户信息

#### 3.2 设置数据库权限
```javascript
// knowledge_articles 集合权限
{
  "read": true,
  "write": "doc._openid == auth.openid"
}

// calculation_history 集合权限
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}

// user_reading_history 集合权限
{
  "read": "doc._openid == auth.openid",
  "write": "doc._openid == auth.openid"
}
```

#### 3.3 初始化示例数据
可以通过云开发控制台导入示例数据，或者创建数据初始化云函数。

### 4. 图片资源配置

#### 4.1 上传图片到云存储
将 `miniprogram/images/` 目录下的图片上传到云存储：

1. 在云开发控制台选择"存储"
2. 创建 `images` 文件夹
3. 上传所需图片文件

#### 4.2 更新图片路径
将代码中的本地图片路径替换为云存储路径：
```javascript
// 替换前
src="/images/baby-icon.png"

// 替换后
src="cloud://your-env-id.your-env-id/images/baby-icon.png"
```

### 5. 小程序配置

#### 5.1 配置服务器域名
在小程序管理后台配置以下域名：
- request合法域名：`https://api.weixin.qq.com`
- uploadFile合法域名：`https://api.weixin.qq.com`
- downloadFile合法域名：`https://api.weixin.qq.com`

#### 5.2 配置业务域名（如需要）
如果需要跳转到外部网页，需要配置业务域名。

### 6. 测试部署

#### 6.1 本地测试
1. 在微信开发者工具中编译项目
2. 使用模拟器测试各项功能
3. 使用真机预览测试

#### 6.2 体验版测试
1. 点击"上传"按钮上传代码
2. 在小程序管理后台设置体验版
3. 邀请测试人员进行测试

### 7. 正式发布

#### 7.1 代码审核
1. 在小程序管理后台提交审核
2. 等待微信官方审核（通常1-7个工作日）

#### 7.2 发布上线
审核通过后，在管理后台点击"发布"即可正式上线。

## 环境变量配置

### 开发环境
```javascript
// app.js
wx.cloud.init({
  env: 'your-dev-env-id'
})
```

### 生产环境
```javascript
// app.js
wx.cloud.init({
  env: 'your-prod-env-id'
})
```

## 监控和维护

### 1. 性能监控
- 使用微信小程序后台的性能监控功能
- 关注页面加载时间、接口响应时间等指标

### 2. 错误监控
- 配置云函数错误告警
- 定期查看错误日志

### 3. 数据备份
- 定期备份云数据库数据
- 设置自动备份策略

## 常见问题

### Q1: 云函数调用失败
**解决方案**：
1. 检查云函数是否正确部署
2. 确认云开发环境ID配置正确
3. 检查云函数权限设置

### Q2: 图片加载失败
**解决方案**：
1. 确认图片已上传到云存储
2. 检查图片路径是否正确
3. 确认云存储权限设置

### Q3: 数据库操作失败
**解决方案**：
1. 检查数据库集合是否存在
2. 确认数据库权限配置
3. 检查数据格式是否正确

## 技术支持

如遇到部署问题，可以通过以下方式获取帮助：
1. 查看微信小程序官方文档
2. 在项目 GitHub 仓库提交 Issue
3. 联系技术支持团队

---

**注意**：部署前请仔细阅读微信小程序的相关政策和规范，确保应用内容符合平台要求。
