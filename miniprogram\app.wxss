/* app.wxss */
/* 全局样式 */
page {
  --primary-color: #4f46e5;
  --primary-light: #6366f1;
  --primary-dark: #3730a3;
  --secondary-color: #10b981;
  --accent-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-card: #ffffff;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 6rpx;
  --radius-md: 8rpx;
  --radius-lg: 12rpx;
  --radius-xl: 16rpx;
  
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-primary);
}

/* 通用容器 */
.container {
  padding: 32rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  margin-bottom: 32rpx;
  overflow: hidden;
}

.card-header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: 32rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.card-title .icon {
  margin-right: 16rpx;
  font-size: 40rpx;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--radius-lg);
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 32rpx;
  background: var(--bg-primary);
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* 数字输入框 */
.number-input {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--bg-primary);
}

.number-input input {
  flex: 1;
  text-align: center;
  border: none;
  padding: 24rpx;
  font-size: 32rpx;
}

.number-input button {
  width: 80rpx;
  height: 80rpx;
  background: var(--bg-secondary);
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-secondary);
}

.number-input button:hover {
  background: var(--border-color);
}

/* 结果样式 */
.result-summary {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.summary-item {
  flex: 1;
  text-align: center;
  padding: 32rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.summary-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--secondary-color);
}

.mb-16 {
  margin-bottom: 16rpx;
}

.mb-24 {
  margin-bottom: 24rpx;
}

.mb-32 {
  margin-bottom: 32rpx;
}
