<!--profile.wxml-->
<page-meta>
  <navigation-bar title="个人中心" back="{{false}}" color="white" background="#4A90E2"></navigation-bar>
</page-meta>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-header">
        <view class="avatar-container">
          <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/placeholder.svg'}}" mode="aspectFill" bindtap="chooseAvatar"></image>
          <view class="avatar-badge" wx:if="{{userInfo.isVip}}">VIP</view>
        </view>
        <view class="user-info">
          <text class="user-name">{{userInfo.nickName || '点击登录'}}</text>
          <text class="user-desc">{{userInfo.desc || '完善信息，获得更精准的补贴计算'}}</text>
        </view>
        <view class="login-btn" wx:if="{{!userInfo.isLogin}}" bindtap="login">
          <text>登录</text>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="user-stats" wx:if="{{userInfo.isLogin}}">
        <view class="stat-item" bindtap="goToCalculationHistory">
          <text class="stat-number">{{userStats.calculationCount || 0}}</text>
          <text class="stat-label">计算次数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" bindtap="goToFavorites">
          <text class="stat-number">{{userStats.favoriteCount || 0}}</text>
          <text class="stat-label">收藏文章</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" bindtap="goToReadingHistory">
          <text class="stat-number">{{userStats.readingCount || 0}}</text>
          <text class="stat-label">阅读记录</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" bindtap="goToCalculationHistory">
          <view class="menu-icon calculation-icon">📊</view>
          <text class="menu-title">计算历史</text>
          <text class="menu-desc">查看历史计算记录</text>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bindtap="goToFavorites">
          <view class="menu-icon favorite-icon">❤️</view>
          <text class="menu-title">我的收藏</text>
          <text class="menu-desc">收藏的育儿知识</text>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bindtap="goToReadingHistory">
          <view class="menu-icon reading-icon">📖</view>
          <text class="menu-title">阅读记录</text>
          <text class="menu-desc">浏览过的文章</text>
          <view class="menu-arrow">></view>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" bindtap="goToSettings">
          <view class="menu-icon settings-icon">⚙️</view>
          <text class="menu-title">设置</text>
          <text class="menu-desc">个人信息与偏好设置</text>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bindtap="goToFeedback">
          <view class="menu-icon feedback-icon">💬</view>
          <text class="menu-title">意见反馈</text>
          <text class="menu-desc">帮助我们改进产品</text>
          <view class="menu-arrow">></view>
        </view>

        <view class="menu-item" bindtap="goToAbout">
          <view class="menu-icon about-icon">ℹ️</view>
          <text class="menu-title">关于我们</text>
          <text class="menu-desc">版本信息与联系方式</text>
          <view class="menu-arrow">></view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions" wx:if="{{userInfo.isLogin}}">
      <view class="action-title">快捷操作</view>
      <view class="action-buttons">
        <button class="action-btn" bindtap="shareApp">
          <view class="action-icon">📤</view>
          <text>分享应用</text>
        </button>
        <button class="action-btn" bindtap="clearCache">
          <view class="action-icon">🗑️</view>
          <text>清理缓存</text>
        </button>
        <button class="action-btn" bindtap="exportData">
          <view class="action-icon">📋</view>
          <text>导出数据</text>
        </button>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" wx:if="{{userInfo.isLogin}}">
      <button class="logout-btn" bindtap="logout">退出登录</button>
    </view>
  </view>
</scroll-view>