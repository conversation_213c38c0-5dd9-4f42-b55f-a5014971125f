<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>育儿补贴计算器 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-case {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-case h3 {
            color: #333;
            margin-top: 0;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        .success {
            background: #e8f5e8;
            color: #080;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3730a3;
        }
    </style>
</head>
<body>
    <h1>🧪 育儿补贴计算器测试</h1>
    
    <div class="test-case">
        <h3>测试案例 1: 2025年出生的孩子</h3>
        <p><strong>场景：</strong>孩子在2025年3月1日出生</p>
        <p><strong>预期：</strong>从出生开始享受补贴，直到2028年3月1日（3岁生日）</p>
        <button onclick="testCase1()">运行测试</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 2: 2024年出生的孩子</h3>
        <p><strong>场景：</strong>孩子在2024年6月15日出生</p>
        <p><strong>预期：</strong>从2025年1月1日开始享受补贴，直到2027年6月15日（3岁生日）</p>
        <button onclick="testCase2()">运行测试</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 3: 已满3岁的孩子</h3>
        <p><strong>场景：</strong>孩子在2021年6月1日出生（政策实施时已满3岁）</p>
        <p><strong>预期：</strong>无补贴，显示"已超过3岁"状态</p>
        <button onclick="testCase3()">运行测试</button>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 4: 多个孩子</h3>
        <p><strong>场景：</strong>两个孩子，分别在2024年1月1日和2025年6月1日出生</p>
        <p><strong>预期：</strong>计算两个孩子的总补贴</p>
        <button onclick="testCase4()">运行测试</button>
        <div id="result4" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 5: 边界情况</h3>
        <p><strong>场景：</strong>孩子在2025年1月1日出生（政策开始当天）</p>
        <p><strong>预期：</strong>从出生当天开始享受补贴</p>
        <button onclick="testCase5()">运行测试</button>
        <div id="result5" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 6: 日期格式验证</h3>
        <p><strong>场景：</strong>验证日期格式化函数是否正确</p>
        <p><strong>预期：</strong>显示正确的中文日期格式</p>
        <button onclick="testCase6()">运行测试</button>
        <div id="result6" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 7: 2020年出生的孩子</h3>
        <p><strong>场景：</strong>孩子在2020年3月15日出生（政策实施时已满4岁）</p>
        <p><strong>预期：</strong>无补贴，显示"已超过3岁"状态，不应该报错</p>
        <button onclick="testCase7()">运行测试</button>
        <div id="result7" class="result"></div>
    </div>

    <div class="test-case">
        <h3>测试案例 8: 边界日期</h3>
        <p><strong>场景：</strong>孩子在2022年1月1日出生（政策实施时刚好3岁）</p>
        <p><strong>预期：</strong>无补贴，因为政策实施时已满3岁</p>
        <button onclick="testCase8()">运行测试</button>
        <div id="result8" class="result"></div>
    </div>

    <script>
        // 复制主应用的计算函数
        function calculateChildSubsidy(child) {
            const now = new Date();
            const birthDate = child.birthDate;
            const thirdBirthday = new Date(birthDate);
            thirdBirthday.setFullYear(thirdBirthday.getFullYear() + 3);
            
            const policyStartDate = new Date('2025-01-01');
            const subsidyStartDate = birthDate > policyStartDate ? birthDate : policyStartDate;
            const subsidyEndDate = thirdBirthday;
            
            if (now >= thirdBirthday || birthDate > now) {
                return {
                    childIndex: child.index,
                    birthDate: birthDate,
                    monthlySubsidy: 0,
                    totalSubsidy: 0,
                    status: now >= thirdBirthday ? '已超过3岁' : '尚未出生',
                    subsidyPeriod: '无补贴期间'
                };
            }
            
            if (subsidyStartDate >= subsidyEndDate) {
                return {
                    childIndex: child.index,
                    birthDate: birthDate,
                    monthlySubsidy: 0,
                    totalSubsidy: 0,
                    status: '政策实施前已满3岁',
                    subsidyPeriod: '无补贴期间'
                };
            }
            
            // 计算补贴月数（按完整月份计算）
            let totalMonths = 0;
            let currentDate = new Date(subsidyStartDate);

            // 计算从开始日期到结束日期之间的完整月份
            while (currentDate < subsidyEndDate) {
                let nextMonth = new Date(currentDate);
                nextMonth.setMonth(nextMonth.getMonth() + 1);

                if (nextMonth <= subsidyEndDate) {
                    totalMonths++;
                } else {
                    // 检查是否还有不足一个月的时间
                    break;
                }

                currentDate = nextMonth;
            }
            const monthlyAmount = 300;
            const totalAmount = totalMonths * monthlyAmount;
            
            let status = '';
            let currentMonthlySubsidy = 0;
            
            if (now < subsidyStartDate) {
                status = '补贴尚未开始';
                currentMonthlySubsidy = 0;
            } else if (now >= subsidyEndDate) {
                status = '补贴已结束';
                currentMonthlySubsidy = 0;
            } else {
                status = '正在享受补贴';
                currentMonthlySubsidy = monthlyAmount;
            }
            
            const subsidyPeriod = `${formatDate(subsidyStartDate)} 至 ${formatDate(subsidyEndDate)}`;
            
            return {
                childIndex: child.index,
                birthDate: birthDate,
                monthlySubsidy: currentMonthlySubsidy,
                totalSubsidy: totalAmount,
                status: status,
                subsidyPeriod: subsidyPeriod,
                totalMonths: totalMonths
            };
        }

        function formatDate(date) {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        function displayTestResult(resultId, result, expected) {
            const resultDiv = document.getElementById(resultId);
            const isSuccess = result.totalSubsidy === expected.totalSubsidy;
            
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h4>${isSuccess ? '✅ 测试通过' : '❌ 测试失败'}</h4>
                <p><strong>出生日期：</strong>${formatDate(result.birthDate)}</p>
                <p><strong>补贴期间：</strong>${result.subsidyPeriod}</p>
                <p><strong>状态：</strong>${result.status}</p>
                <p><strong>每月补贴：</strong>¥${result.monthlySubsidy}</p>
                <p><strong>总补贴：</strong>¥${result.totalSubsidy}</p>
                <p><strong>补贴月数：</strong>${result.totalMonths || 0}个月</p>
                ${!isSuccess ? `<p><strong>预期总补贴：</strong>¥${expected.totalSubsidy}</p>` : ''}
            `;
        }

        function testCase1() {
            const child = { index: 1, birthDate: new Date('2025-03-01') };
            const result = calculateChildSubsidy(child);
            const expected = { totalSubsidy: 10800 }; // 36个月 * 300元
            displayTestResult('result1', result, expected);
        }

        function testCase2() {
            const child = { index: 1, birthDate: new Date('2024-06-15') };
            const result = calculateChildSubsidy(child);
            const expected = { totalSubsidy: 7500 }; // 25个月 * 300元 (2025-01-01 到 2027-06-15)
            displayTestResult('result2', result, expected);
        }

        function testCase3() {
            const child = { index: 1, birthDate: new Date('2021-06-01') };
            const result = calculateChildSubsidy(child);
            const expected = { totalSubsidy: 0 };
            displayTestResult('result3', result, expected);
        }

        function testCase4() {
            const child1 = { index: 1, birthDate: new Date('2024-01-01') };
            const child2 = { index: 2, birthDate: new Date('2025-06-01') };
            const result1 = calculateChildSubsidy(child1);
            const result2 = calculateChildSubsidy(child2);
            
            const totalSubsidy = result1.totalSubsidy + result2.totalSubsidy;
            const resultDiv = document.getElementById('result4');
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <h4>✅ 多孩子测试结果</h4>
                <p><strong>第1个孩子：</strong>¥${result1.totalSubsidy} (${result1.totalMonths}个月)</p>
                <p><strong>第2个孩子：</strong>¥${result2.totalSubsidy} (${result2.totalMonths}个月)</p>
                <p><strong>总补贴：</strong>¥${totalSubsidy}</p>
            `;
        }

        function testCase5() {
            const child = { index: 1, birthDate: new Date('2025-01-01') };
            const result = calculateChildSubsidy(child);
            const expected = { totalSubsidy: 10800 }; // 36个月 * 300元
            displayTestResult('result5', result, expected);
        }

        function testCase6() {
            const testDates = [
                new Date('2024-01-01'),
                new Date('2025-06-15'),
                new Date('2023-12-31')
            ];

            const resultDiv = document.getElementById('result6');
            resultDiv.className = 'result success';

            let html = '<h4>✅ 日期格式测试</h4>';
            testDates.forEach((date, index) => {
                const formatted = formatDate(date);
                html += `<p><strong>测试${index + 1}：</strong>${formatted}</p>`;
            });

            // 验证格式是否正确（应该是 YYYY/MM/DD 格式）
            const testFormat = formatDate(new Date('2024-03-05'));
            const isCorrectFormat = /^\d{4}\/\d{2}\/\d{2}$/.test(testFormat);

            html += `<p><strong>格式验证：</strong>${isCorrectFormat ? '✅ 正确' : '❌ 错误'} (${testFormat})</p>`;

            resultDiv.innerHTML = html;
        }

        function testCase7() {
            const child = { index: 1, birthDate: new Date('2020-03-15') };
            const result = calculateChildSubsidy(child);
            const expected = { totalSubsidy: 0 };

            const resultDiv = document.getElementById('result7');
            const isSuccess = result.totalSubsidy === expected.totalSubsidy && result.status === '已超过3岁';

            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h4>${isSuccess ? '✅ 测试通过' : '❌ 测试失败'}</h4>
                <p><strong>出生日期：</strong>${formatDate(result.birthDate)}</p>
                <p><strong>补贴期间：</strong>${result.subsidyPeriod}</p>
                <p><strong>状态：</strong>${result.status}</p>
                <p><strong>每月补贴：</strong>¥${result.monthlySubsidy}</p>
                <p><strong>总补贴：</strong>¥${result.totalSubsidy}</p>
                <p><strong>补贴月数：</strong>${result.totalMonths || 0}个月</p>
                <p><strong>说明：</strong>2020年出生的孩子可以正常计算，不会报错，显示"已超过3岁"</p>
                ${!isSuccess ? `<p><strong>预期总补贴：</strong>¥${expected.totalSubsidy}</p>` : ''}
            `;
        }

        function testCase8() {
            const child = { index: 1, birthDate: new Date('2022-01-01') };
            const result = calculateChildSubsidy(child);
            const expected = { totalSubsidy: 0 };

            const resultDiv = document.getElementById('result8');
            const isSuccess = result.totalSubsidy === expected.totalSubsidy && result.status === '已超过3岁';

            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h4>${isSuccess ? '✅ 测试通过' : '❌ 测试失败'}</h4>
                <p><strong>出生日期：</strong>${formatDate(result.birthDate)}</p>
                <p><strong>补贴期间：</strong>${result.subsidyPeriod}</p>
                <p><strong>状态：</strong>${result.status}</p>
                <p><strong>每月补贴：</strong>¥${result.monthlySubsidy}</p>
                <p><strong>总补贴：</strong>¥${result.totalSubsidy}</p>
                <p><strong>补贴月数：</strong>${result.totalMonths || 0}个月</p>
                <p><strong>说明：</strong>2022年1月1日出生，政策实施时刚好3岁，无补贴</p>
                ${!isSuccess ? `<p><strong>预期总补贴：</strong>¥${expected.totalSubsidy}</p>` : ''}
            `;
        }
    </script>
</body>
</html>
