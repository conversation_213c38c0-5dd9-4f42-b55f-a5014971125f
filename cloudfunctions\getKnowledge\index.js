// 云函数：获取育儿知识
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { 
    category = 'all', 
    keyword = '', 
    page = 1, 
    pageSize = 10,
    type = 'list' // list: 列表, detail: 详情, related: 相关推荐
  } = event

  try {
    if (type === 'detail') {
      // 获取文章详情
      const { id } = event
      const result = await db.collection('knowledge_articles')
        .doc(id)
        .get()
      
      if (result.data) {
        // 增加阅读量
        await db.collection('knowledge_articles')
          .doc(id)
          .update({
            data: {
              viewCount: db.command.inc(1)
            }
          })
        
        // 记录用户阅读历史
        const openid = cloud.getWXContext().OPENID
        if (openid) {
          await db.collection('user_reading_history').add({
            data: {
              openid,
              articleId: id,
              readTime: new Date()
            }
          })
        }
      }
      
      return {
        success: true,
        data: result.data
      }
    }
    
    if (type === 'related') {
      // 获取相关推荐
      const { articleId, category: articleCategory } = event
      const result = await db.collection('knowledge_articles')
        .where({
          _id: db.command.neq(articleId),
          category: articleCategory,
          status: 'published'
        })
        .orderBy('viewCount', 'desc')
        .limit(5)
        .get()
      
      return {
        success: true,
        data: result.data
      }
    }
    
    // 获取文章列表
    let query = db.collection('knowledge_articles')
      .where({
        status: 'published'
      })
    
    // 分类筛选
    if (category !== 'all') {
      const categoryMap = {
        'newborn': ['0-1岁', '新生儿护理'],
        'toddler': ['1-3岁', '早期教育'],
        'preschool': ['3-6岁', '性格培养'],
        'health': ['健康防护', '健康'],
        'nutrition': ['营养喂养', '营养'],
        'education': ['早期教育', '教育'],
        'psychology': ['性格培养', '心理']
      }
      
      const categories = categoryMap[category] || [category]
      query = query.where({
        category: db.command.in(categories)
      })
    }
    
    // 关键词搜索
    if (keyword) {
      query = query.where(db.command.or([
        {
          title: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        },
        {
          summary: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        },
        {
          tags: db.command.in([keyword])
        }
      ]))
    }
    
    // 分页查询
    const skip = (page - 1) * pageSize
    const result = await query
      .orderBy('publishTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()
    
    // 获取总数
    const countResult = await query.count()
    const total = countResult.total
    const hasMore = skip + result.data.length < total
    
    return {
      success: true,
      data: {
        list: result.data,
        total,
        hasMore,
        page,
        pageSize
      }
    }
    
  } catch (error) {
    console.error('获取育儿知识时出错:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
