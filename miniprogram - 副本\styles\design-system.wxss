/* 设计系统 - Design System */

/* ========== 颜色系统 ========== */
:root {
  /* 主色调 */
  --primary-color: #4A90E2;
  --primary-light: #6BA3E8;
  --primary-dark: #357ABD;
  --primary-gradient: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  
  /* 辅助色 */
  --secondary-color: #81C784;
  --accent-color: #FFB74D;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --success-color: #4CAF50;
  
  /* 中性色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #CCCCCC;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F5F5F5;
  --bg-tertiary: #F8F9FA;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* 边框色 */
  --border-light: #F0F0F0;
  --border-medium: #E0E0E0;
  --border-dark: #CCCCCC;
  
  /* 阴影 */
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-primary: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
}

/* ========== 字体系统 ========== */
.text-xs { font-size: 20rpx; line-height: 1.4; }
.text-sm { font-size: 24rpx; line-height: 1.4; }
.text-base { font-size: 28rpx; line-height: 1.5; }
.text-lg { font-size: 32rpx; line-height: 1.5; }
.text-xl { font-size: 36rpx; line-height: 1.4; }
.text-2xl { font-size: 40rpx; line-height: 1.3; }
.text-3xl { font-size: 48rpx; line-height: 1.2; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* ========== 间距系统 ========== */
.p-xs { padding: 8rpx; }
.p-sm { padding: 16rpx; }
.p-base { padding: 24rpx; }
.p-lg { padding: 32rpx; }
.p-xl { padding: 40rpx; }

.m-xs { margin: 8rpx; }
.m-sm { margin: 16rpx; }
.m-base { margin: 24rpx; }
.m-lg { margin: 32rpx; }
.m-xl { margin: 40rpx; }

/* ========== 圆角系统 ========== */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 8rpx; }
.rounded-base { border-radius: 12rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-xl { border-radius: 20rpx; }
.rounded-full { border-radius: 50%; }

/* ========== 组件基础样式 ========== */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-primary);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 2rpx solid var(--border-medium);
}

.btn-secondary:active {
  background: var(--border-light);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:active {
  background: var(--primary-color);
  color: white;
}

/* 卡片组件 */
.card {
  background: var(--bg-primary);
  border-radius: 16rpx;
  box-shadow: var(--shadow-medium);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-light);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background: var(--bg-tertiary);
  border-top: 1rpx solid var(--border-light);
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: 20rpx 24rpx;
  background: var(--bg-tertiary);
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.input:focus {
  background: var(--bg-primary);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(74, 144, 226, 0.1);
}

.input::placeholder {
  color: var(--text-tertiary);
}

/* 标签组件 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.tag-primary {
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background: var(--border-light);
  margin: 24rpx 0;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: var(--text-tertiary);
  font-size: 26rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

/* ========== 布局系统 ========== */
.container {
  padding: 0 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* ========== 响应式系统 ========== */
@media (max-width: 375px) {
  .container {
    padding: 0 20rpx;
  }
  
  .text-base { font-size: 26rpx; }
  .text-lg { font-size: 30rpx; }
  .text-xl { font-size: 34rpx; }
}

/* ========== 动画系统 ========== */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* ========== 工具类 ========== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.visible { display: block; }

.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
