// 云函数：计算育儿补贴
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 补贴政策配置
const SUBSIDY_POLICIES = {
  // 北京市政策
  'beijing': {
    birthAllowance: {
      base: 2000,
      maxIncome: 20000,
      socialSecurityRequired: true
    },
    childcareAllowance: {
      perChild: 500,
      maxChildren: 3,
      ageLimit: 3
    },
    nurseryAllowance: {
      perChild: 300,
      incomeLimit: 15000,
      ageLimit: 3
    },
    educationAllowance: {
      perChild: 200,
      ageRange: [3, 18]
    }
  },
  // 上海市政策
  'shanghai': {
    birthAllowance: {
      base: 1800,
      maxIncome: 18000,
      socialSecurityRequired: true
    },
    childcareAllowance: {
      perChild: 600,
      maxChildren: 3,
      ageLimit: 3
    },
    nurseryAllowance: {
      perChild: 400,
      incomeLimit: 16000,
      ageLimit: 3
    },
    educationAllowance: {
      perChild: 250,
      ageRange: [3, 18]
    }
  },
  // 默认政策
  'default': {
    birthAllowance: {
      base: 1000,
      maxIncome: 15000,
      socialSecurityRequired: true
    },
    childcareAllowance: {
      perChild: 300,
      maxChildren: 3,
      ageLimit: 3
    },
    nurseryAllowance: {
      perChild: 200,
      incomeLimit: 12000,
      ageLimit: 3
    },
    educationAllowance: {
      perChild: 150,
      ageRange: [3, 18]
    }
  }
}

exports.main = async (event, context) => {
  const { 
    region, 
    monthlyIncome, 
    childCount, 
    childAges, 
    employmentStatus, 
    socialSecurityStatus 
  } = event

  try {
    // 获取对应地区的政策
    const regionKey = region.toLowerCase().replace(/[^a-z]/g, '')
    const policy = SUBSIDY_POLICIES[regionKey] || SUBSIDY_POLICIES['default']
    
    const results = []
    let totalAmount = 0

    // 计算生育津贴
    if (socialSecurityStatus === '正常缴费' && monthlyIncome <= policy.birthAllowance.maxIncome) {
      const amount = Math.min(monthlyIncome * 0.3, policy.birthAllowance.base)
      results.push({
        type: 'birth',
        name: '生育津贴',
        description: '符合生育政策的家庭可申请',
        amount: Math.round(amount),
        conditions: ['正常缴费社保', `月收入不超过${policy.birthAllowance.maxIncome}元`]
      })
      totalAmount += amount
    }

    // 计算育儿补贴
    if (childCount > 0 && childCount <= policy.childcareAllowance.maxChildren) {
      const eligibleChildren = childAges.filter(age => {
        const ageNum = parseInt(age.split('-')[0])
        return ageNum <= policy.childcareAllowance.ageLimit
      }).length
      
      if (eligibleChildren > 0) {
        const amount = policy.childcareAllowance.perChild * eligibleChildren
        results.push({
          type: 'childcare',
          name: '育儿补贴',
          description: `${eligibleChildren}个符合条件孩子的育儿补贴`,
          amount: amount,
          conditions: [`${policy.childcareAllowance.ageLimit}岁以下儿童`, `最多${policy.childcareAllowance.maxChildren}个孩子`]
        })
        totalAmount += amount
      }
    }

    // 计算托育补贴
    if (monthlyIncome < policy.nurseryAllowance.incomeLimit) {
      const eligibleChildren = childAges.filter(age => {
        const ageNum = parseInt(age.split('-')[0])
        return ageNum <= policy.nurseryAllowance.ageLimit
      }).length
      
      if (eligibleChildren > 0) {
        const amount = policy.nurseryAllowance.perChild * eligibleChildren
        results.push({
          type: 'nursery',
          name: '托育补贴',
          description: '3岁以下婴幼儿托育服务补贴',
          amount: amount,
          conditions: [`月收入低于${policy.nurseryAllowance.incomeLimit}元`, '使用正规托育服务']
        })
        totalAmount += amount
      }
    }

    // 计算教育补贴
    if (childCount > 0) {
      const eligibleChildren = childAges.filter(age => {
        const ageNum = parseInt(age.split('-')[0])
        return ageNum >= policy.educationAllowance.ageRange[0] && 
               ageNum <= policy.educationAllowance.ageRange[1]
      }).length
      
      if (eligibleChildren > 0) {
        const amount = policy.educationAllowance.perChild * eligibleChildren
        results.push({
          type: 'education',
          name: '教育补贴',
          description: '学前教育及义务教育补贴',
          amount: amount,
          conditions: [`${policy.educationAllowance.ageRange[0]}-${policy.educationAllowance.ageRange[1]}岁儿童`, '在读学生']
        })
        totalAmount += amount
      }
    }

    // 保存计算记录到数据库
    await db.collection('calculation_history').add({
      data: {
        openid: cloud.getWXContext().OPENID,
        region,
        monthlyIncome,
        childCount,
        childAges,
        employmentStatus,
        socialSecurityStatus,
        results,
        totalAmount,
        createTime: new Date()
      }
    })

    return {
      success: true,
      data: {
        results,
        totalAmount: Math.round(totalAmount),
        region,
        calculationTime: new Date().toISOString()
      }
    }

  } catch (error) {
    console.error('计算补贴时出错:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
