{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript", "sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": false, "enhance": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "2.32.3", "packOptions": {"ignore": [], "include": []}, "appid": "wxd9db9be135ac6e4d"}