// knowledge.ts
Component({
  data: {
    searchKeyword: '',
    currentCategory: 'all',
    categories: [
      { id: 'all', name: '全部' },
      { id: 'newborn', name: '0-1岁' },
      { id: 'toddler', name: '1-3岁' },
      { id: 'preschool', name: '3-6岁' },
      { id: 'health', name: '健康' },
      { id: 'nutrition', name: '营养' },
      { id: 'education', name: '教育' },
      { id: 'psychology', name: '心理' }
    ],
    knowledgeList: [
      {
        id: 1,
        title: '新生儿护理基础知识大全',
        summary: '从喂养到睡眠，从洗澡到换尿布，全面了解新生儿护理的各个方面，让新手父母更加从容应对。',
        category: '新生儿护理',
        ageGroup: '0-1岁',
        author: '李医生',
        publishTime: '2天前',
        viewCount: '1.2k',
        likeCount: '89',
        thumbnail: '/images/knowledge-1.jpg'
      },
      {
        id: 2,
        title: '幼儿早教启蒙的科学方法',
        summary: '科学的早教方法能够有效促进宝宝的智力发育，本文详细介绍了适合1-3岁幼儿的早教活动。',
        category: '早期教育',
        ageGroup: '1-3岁',
        author: '王老师',
        publishTime: '3天前',
        viewCount: '856',
        likeCount: '67',
        thumbnail: '/images/knowledge-2.jpg'
      },
      {
        id: 3,
        title: '婴幼儿疫苗接种完整时间表',
        summary: '详细的疫苗接种计划表，包括各种疫苗的接种时间、注意事项和可能的副作用。',
        category: '健康防护',
        ageGroup: '0-6岁',
        author: '张医生',
        publishTime: '5天前',
        viewCount: '2.1k',
        likeCount: '156',
        thumbnail: '/images/knowledge-3.jpg'
      },
      {
        id: 4,
        title: '宝宝辅食添加的正确顺序',
        summary: '6个月后如何科学添加辅食，从单一到复合，从稀到稠，循序渐进培养宝宝的饮食习惯。',
        category: '营养喂养',
        ageGroup: '6个月+',
        author: '营养师小刘',
        publishTime: '1周前',
        viewCount: '1.8k',
        likeCount: '134',
        thumbnail: '/images/knowledge-4.jpg'
      },
      {
        id: 5,
        title: '如何培养孩子的独立性',
        summary: '从小培养孩子的独立性对其成长至关重要，本文分享实用的培养方法和注意事项。',
        category: '性格培养',
        ageGroup: '2-6岁',
        author: '心理咨询师陈老师',
        publishTime: '1周前',
        viewCount: '943',
        likeCount: '78',
        thumbnail: '/images/knowledge-5.jpg'
      }
    ],
    filteredList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },
  
  lifetimes: {
    attached() {
      this.setData({
        filteredList: this.data.knowledgeList
      })
    }
  },
  
  methods: {
    // 搜索输入
    onSearchInput(e: any) {
      this.setData({
        searchKeyword: e.detail.value
      })
    },
    
    // 执行搜索
    onSearch() {
      const keyword = this.data.searchKeyword.trim()
      this.filterKnowledge(this.data.currentCategory, keyword)
    },
    
    // 分类切换
    onCategoryChange(e: any) {
      const categoryId = e.currentTarget.dataset.id
      this.setData({
        currentCategory: categoryId
      })
      this.filterKnowledge(categoryId, this.data.searchKeyword)
    },
    
    // 过滤知识内容
    filterKnowledge(category: string, keyword: string) {
      let filtered = [...this.data.knowledgeList]
      
      // 按分类过滤
      if (category !== 'all') {
        const categoryMap: { [key: string]: string[] } = {
          'newborn': ['0-1岁', '新生儿护理'],
          'toddler': ['1-3岁', '早期教育'],
          'preschool': ['3-6岁', '性格培养'],
          'health': ['健康防护', '健康'],
          'nutrition': ['营养喂养', '营养'],
          'education': ['早期教育', '教育'],
          'psychology': ['性格培养', '心理']
        }
        
        const targetCategories = categoryMap[category] || []
        filtered = filtered.filter(item => 
          targetCategories.some(cat => 
            item.category.includes(cat) || item.ageGroup?.includes(cat)
          )
        )
      }
      
      // 按关键词过滤
      if (keyword) {
        filtered = filtered.filter(item =>
          item.title.includes(keyword) ||
          item.summary.includes(keyword) ||
          item.category.includes(keyword)
        )
      }
      
      this.setData({
        filteredList: filtered
      })
    },
    
    // 跳转到详情页
    goToDetail(e: any) {
      const id = e.currentTarget.dataset.id
      wx.navigateTo({
        url: `/pages/knowledge-detail/knowledge-detail?id=${id}`
      })
    },
    
    // 加载更多
    loadMore() {
      if (this.data.loading || !this.data.hasMore) {
        return
      }
      
      this.setData({
        loading: true
      })
      
      // 模拟加载更多数据
      setTimeout(() => {
        // 这里应该调用API获取更多数据
        // 暂时模拟没有更多数据
        this.setData({
          loading: false,
          hasMore: false
        })
      }, 1000)
    },
    
    // 跳转到收藏页面
    goToFavorites() {
      wx.navigateTo({
        url: '/pages/favorites/favorites'
      })
    }
  }
})
