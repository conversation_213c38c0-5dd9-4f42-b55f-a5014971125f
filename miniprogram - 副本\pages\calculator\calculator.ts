// calculator.ts
Component({
  data: {
    // 地区数据
    regionData: [
      ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉'],
      ['朝阳区', '海淀区', '西城区', '东城区', '丰台区', '石景山区']
    ],
    regionIndex: [0, 0],
    selectedRegion: '',
    
    // 孩子数量选项
    childCountOptions: ['请选择', '1个', '2个', '3个', '4个或以上'],
    childCountIndex: 0,
    childAges: [],
    
    // 年龄选项
    ageOptions: ['0-6个月', '6-12个月', '1-2岁', '2-3岁', '3-6岁', '6岁以上'],
    
    // 收入信息
    monthlyIncome: '',
    
    // 就业状态选项
    employmentOptions: ['请选择', '在职员工', '自由职业', '个体经营', '暂时失业', '全职妈妈/爸爸'],
    employmentIndex: 0,
    
    // 社保缴费选项
    socialSecurityOptions: ['请选择', '正常缴费', '断缴中', '从未缴费'],
    socialSecurityIndex: 0,
    
    // 计算结果
    showResult: false,
    subsidyResults: [],
    totalSubsidy: 0,
    applySteps: []
  },
  
  methods: {
    // 地区选择
    onRegionChange(e: any) {
      const value = e.detail.value
      const regionData = this.data.regionData
      const selectedRegion = `${regionData[0][value[0]]} ${regionData[1][value[1]]}`
      
      this.setData({
        regionIndex: value,
        selectedRegion: selectedRegion
      })
    },
    
    // 孩子数量选择
    onChildCountChange(e: any) {
      const index = e.detail.value
      const count = index > 0 ? index : 0
      const ages = new Array(count).fill(0)
      
      this.setData({
        childCountIndex: index,
        childAges: ages
      })
    },
    
    // 孩子年龄选择
    onChildAgeChange(e: any) {
      const value = e.detail.value
      const index = e.currentTarget.dataset.index
      const ages = [...this.data.childAges]
      ages[index] = value
      
      this.setData({
        childAges: ages
      })
    },
    
    // 收入输入
    onIncomeInput(e: any) {
      this.setData({
        monthlyIncome: e.detail.value
      })
    },
    
    // 就业状态选择
    onEmploymentChange(e: any) {
      this.setData({
        employmentIndex: e.detail.value
      })
    },
    
    // 社保缴费选择
    onSocialSecurityChange(e: any) {
      this.setData({
        socialSecurityIndex: e.detail.value
      })
    },
    
    // 计算补贴
    calculateSubsidy() {
      // 验证必填项
      if (!this.validateForm()) {
        return
      }
      
      // 模拟计算逻辑
      const results = this.performCalculation()
      const total = results.reduce((sum, item) => sum + item.amount, 0)
      
      this.setData({
        subsidyResults: results,
        totalSubsidy: total,
        showResult: true,
        applySteps: [
          '准备相关证明材料（身份证、户口本、收入证明等）',
          '前往当地社区服务中心或通过官方APP申请',
          '填写申请表格并提交材料',
          '等待审核结果（通常7-15个工作日）',
          '审核通过后，补贴将发放到指定银行账户'
        ]
      })
      
      // 滚动到结果区域
      wx.pageScrollTo({
        selector: '.result-section',
        duration: 500
      })
    },
    
    // 表单验证
    validateForm() {
      if (this.data.regionIndex[0] === 0 && this.data.regionIndex[1] === 0 && !this.data.selectedRegion) {
        wx.showToast({
          title: '请选择所在地区',
          icon: 'none'
        })
        return false
      }
      
      if (this.data.childCountIndex === 0) {
        wx.showToast({
          title: '请选择孩子数量',
          icon: 'none'
        })
        return false
      }
      
      if (!this.data.monthlyIncome) {
        wx.showToast({
          title: '请输入家庭月收入',
          icon: 'none'
        })
        return false
      }
      
      if (this.data.employmentIndex === 0) {
        wx.showToast({
          title: '请选择就业状态',
          icon: 'none'
        })
        return false
      }
      
      return true
    },
    
    // 执行计算逻辑
    performCalculation() {
      const income = parseInt(this.data.monthlyIncome)
      const childCount = this.data.childCountIndex
      const results = []
      
      // 生育津贴
      if (this.data.socialSecurityIndex === 1) { // 正常缴费
        results.push({
          name: '生育津贴',
          description: '符合生育政策的家庭可申请',
          amount: Math.min(income * 0.3, 2000)
        })
      }
      
      // 育儿补贴
      if (childCount > 0) {
        const baseAmount = childCount === 1 ? 500 : childCount === 2 ? 800 : 1200
        results.push({
          name: '育儿补贴',
          description: `${childCount}个孩子的育儿补贴`,
          amount: baseAmount
        })
      }
      
      // 托育补贴
      if (childCount > 0 && income < 15000) {
        results.push({
          name: '托育补贴',
          description: '3岁以下婴幼儿托育服务补贴',
          amount: 300 * childCount
        })
      }
      
      // 教育补贴
      if (childCount > 0) {
        results.push({
          name: '教育补贴',
          description: '学前教育及义务教育补贴',
          amount: 200 * childCount
        })
      }
      
      return results
    },
    
    // 保存结果
    saveResult() {
      // 这里可以实现保存到本地存储或云端的逻辑
      wx.showToast({
        title: '结果已保存',
        icon: 'success'
      })
    },
    
    // 分享结果
    shareResult() {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  }
})
